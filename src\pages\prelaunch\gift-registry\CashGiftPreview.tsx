import { useState } from 'react';
import { ArrowCircleRight2 } from 'iconsax-react';
// import phone from "../../../assets/images/phone.png";
import stackMoney from '../../../assets/images/stack-money2.svg';
import { Success } from './success';
import {
  GiftRegistryServices,
  CreateCashGiftPayload,
} from '../../../lib/services/gift-registry';
import { useEventStore } from '../../../lib/store/event';
// import { Icon } from "../../../components/Icon";

interface CashGift {
  id: number;
  amount: string;
  description: string;
}

// interface AccountDetails {
//   accountNumber: string;
//   bank: string;
//   accountName: string;
//   location: string;
// }

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];

  cashGifts?: CashGift[];
}

interface CashGiftPreviewProps {
  initialData?: RegistryData;
  onClose?: () => void;
  eventId?: string;
}

export const CashGiftPreview = ({
  initialData = {},
  onClose = () => {},
  eventId,
}: CashGiftPreviewProps) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { selectedEvent } = useEventStore();

  // const accountDetails: AccountDetails = {
  //   accountNumber: "**********",
  //   bank: "Guaranty Trust Bank",
  //   accountName: "Oladele Olanrewaju",
  //   location: "Lagos, Nigeria",
  // };

  const handleRemoveCashGift = (id: number) => {
    // In a real app, you would implement this to remove the cash gift
    console.log(`Remove cash gift with id: ${id}`);
  };

  const handleCreateGiftRegistry = async () => {
    const currentEventId = eventId || selectedEvent?.id;

    if (!currentEventId) {
      setError('No event selected. Please select an event first.');
      return;
    }

    if (!cashGifts || cashGifts.length === 0) {
      setError('No cash gifts to create.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Format cash gifts to match the API payload
      const payload: CreateCashGiftPayload = {
        gifts: cashGifts.map((gift) => ({
          amount: gift.amount.replace(/,/g, ''), // Remove commas from amount
          description: gift.description,
          is_crowd_gift: false, // You can add this as a property to CashGift interface if needed
        })),
      };

      await GiftRegistryServices.createCashGift(currentEventId, payload);
      setOpen(true); // Show success modal
    } catch (err) {
      console.error('Error creating cash gift registry:', err);
      setError('Failed to create gift registry. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Use initialData.cashGifts if available, otherwise use default cash gifts
  const defaultCashGifts: CashGift[] = [
    {
      id: 1,
      amount: '4,500,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    {
      id: 2,
      amount: '500,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    {
      id: 3,
      amount: '50,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    {
      id: 4,
      amount: '160,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    {
      id: 5,
      amount: '50,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    {
      id: 6,
      amount: '160,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
  ];

  const cashGifts: CashGift[] = initialData.cashGifts?.length
    ? initialData.cashGifts
    : defaultCashGifts;

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0">
        <div className="max-w-[560px] mx-auto relative">
          <h1 className="text-[28px] font-semibold mb-4 mt-5">
            {initialData.registryTitle || "Oladele's birthday gifts"}
          </h1>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
              {error}
            </div>
          )}

          <button
            type="button"
            onClick={handleCreateGiftRegistry}
            disabled={isLoading}
            className="bg-primary text-[12px] md:text-base font-semibold absolute top-[-60px] md:top-0 right-[0] md:right-[-215px] cursor-pointer text-white rounded-full py-2.5 px-2 md:px-4 flex items-center gap-2 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
            {isLoading ? 'Creating...' : 'Create Gift Registry'}
            <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
          </button>

          <div className="flex gap-2 mb-4">
            <span className="text-primary text-sm bg-primary-250 px-3 py-1 rounded-full">
              Cash gift
            </span>
            <span className="text-primary text-sm bg-primary-250 px-3 py-1 rounded-full">
              8 Items
            </span>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {cashGifts.map((cashGift) => (
              <div
                key={cashGift.id}
                className="bg-[#F5F9FF] rounded-xl p-4 relative">
                <button
                  className="absolute top-2 right-2 bg-white rounded-full p-1"
                  onClick={() => handleRemoveCashGift(cashGift.id)}>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 28 28"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      opacity="0.4"
                      d="M14.0026 25.6668C20.4459 25.6668 25.6693 20.4435 25.6693 14.0002C25.6693 7.55684 20.4459 2.3335 14.0026 2.3335C7.55928 2.3335 2.33594 7.55684 2.33594 14.0002C2.33594 20.4435 7.55928 25.6668 14.0026 25.6668Z"
                      fill="#365B96"
                    />
                    <path
                      d="M15.2385 13.9999L17.9219 11.3166C18.2602 10.9783 18.2602 10.4183 17.9219 10.0799C17.5835 9.74159 17.0235 9.74159 16.6852 10.0799L14.0019 12.7633L11.3185 10.0799C10.9802 9.74159 10.4202 9.74159 10.0819 10.0799C9.74354 10.4183 9.74354 10.9783 10.0819 11.3166L12.7652 13.9999L10.0819 16.6833C9.74354 17.0216 9.74354 17.5816 10.0819 17.9199C10.2569 18.0949 10.4785 18.1766 10.7002 18.1766C10.9219 18.1766 11.1435 18.0949 11.3185 17.9199L14.0019 15.2366L16.6852 17.9199C16.8602 18.0949 17.0819 18.1766 17.3035 18.1766C17.5252 18.1766 17.7469 18.0949 17.9219 17.9199C18.2602 17.5816 18.2602 17.0216 17.9219 16.6833L15.2385 13.9999Z"
                      fill="#365B96"
                    />
                  </svg>
                </button>
                <img
                  src={stackMoney}
                  alt="Cash gift"
                  className="w-16 h-16 mb-2"
                />
                <p className="text-xl font-bold">₦{cashGift.amount}</p>
                <p className="text-gray-500 text-sm mt-1">
                  {cashGift.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
      {open && <Success onClose={onClose} />}
    </>
  );
};
