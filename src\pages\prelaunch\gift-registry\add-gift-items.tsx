import {
  Add,
  AddCircle,
  ArrowCircleLeft2,
  ArrowCircleRight2,
  <PERSON><PERSON><PERSON>,
  Minus,
} from 'iconsax-react';
import { useState, useRef, useEffect } from 'react';
import img from '../../../assets/images/gift-img.png';
import { TextInput } from '../../../components/inputs/text-input/text-input';
import { Button } from '../../../components/button/onboardingButton';
import ex from '../../../assets/images/ex1.png';
import { PreviewGiftItems } from './preview-gift-items';
import { ItemAddedModal, SuggestionModal } from './item-added-modal';
import { SingleItemWarningModal } from '../../../components/modals/SingleItemWarningModal';
import { FollowupModal } from '../../../components/modals/followupModal';
interface GiftItemData {
  giftName?: string;
  price?: string;
  quantity?: number;
  description?: string;
  link?: string;
  addToQueue?: boolean;
  giftItems?: GiftItem[];
  giftTypes?: string[];
}

interface AddGiftItemsProps {
  onNextStep: (data: GiftItemData) => void;
  initialData?: GiftItemData;
}

interface GiftItem {
  id: number;
  image: string;
  name: string;
  price?: string;
  quantity?: number;
  description?: string;
  link?: string;
}

export const AddGiftItems = ({
  initialData = {},
  onNextStep,
}: AddGiftItemsProps) => {
  const placeholderItems: GiftItem[] = [
    { id: 1, image: ex, name: 'Phone' },
    // { id: 2, image: ex, name: "Tablet" },
    // { id: 3, image: ex, name: "Laptop" },
    // { id: 4, image: ex, name: "Monitor" },
    // { id: 5, image: ex, name: "Glasses" },
  ];

  const [giftName, setGiftName] = useState(initialData.giftName || '');
  const [price, setPrice] = useState(initialData.price || '');
  const [quantity, setQuantity] = useState(initialData.quantity || 1);
  const [description, setDescription] = useState(initialData.description || '');
  const [link, setLink] = useState(initialData.link || '');
  const [allItems, setAllItems] = useState<GiftItem[]>(
    placeholderItems.slice(0, 5)
  );
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedItem, setSelectedItem] = useState<GiftItem | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isBlurring, setIsBlurring] = useState(false);
  const [mostWanted, setMostWanted] = useState(false);
  const [preview, setPreview] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(
    window.innerWidth < 640 ? 1 : 5
  );
  const [showItemAddedModal, setShowItemAddedModal] = useState(false);
  const [showSuggestionModal, setShowSuggestionModal] = useState(false);
  const [lastAddedItem, setLastAddedItem] = useState<GiftItem | null>(null);
  const [showSingleItemWarningModal, setShowSingleItemWarningModal] =
    useState(false);
  const [showFollowupModal, setShowFollowupModal] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setItemsPerPage(window.innerWidth < 640 ? 1 : 5);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleContinue = () => {
    setIsBlurring(true);
    setPreview(true);
  };

  const handleBack = () => {
    setIsBlurring(false);
    setPreview(false);
  };

  const handleNextStep = () => {
    const hasOnlyOneItem = allItems.length === 1;

    if (hasOnlyOneItem) {
      setShowSingleItemWarningModal(true);
      return;
    }

    if (onNextStep) {
      console.log('Moving to next step with items:', allItems);
      onNextStep({
        giftItems: allItems,
      });
    }
  };

  const handleItemSelect = (item: GiftItem) => {
    setSelectedItem(item);
    setGiftName(item.name || '');
    setPrice(item.price || '');
    setQuantity(item.quantity || 1);
    setDescription(item.description || '');
    setLink(item.link || '');
  };

  const handleAddToQueue = () => {
    if (!giftName) return;

    const newItem: GiftItem = {
      id: Date.now(),
      name: giftName,
      price,
      quantity,
      description,
      link,
      image: selectedItem?.image || ex,
    };

    setAllItems((prev) => [...prev, newItem]);
    setLastAddedItem(newItem);

    setShowSingleItemWarningModal(false);
    setShowFollowupModal(false);

    setGiftName('');
    setPrice('');
    setQuantity(1);
    setDescription('');
    setLink('');
    setSelectedItem(null);
  };

  const handleProceedToCashGift = () => {
    setShowItemAddedModal(false);
    if (onNextStep) {
      console.log('Proceeding to cash gifts with items:', allItems);
      onNextStep({
        giftItems: allItems,
        addToQueue: true,
      });
    }
  };

  const handleContinueAddingItems = () => {
    setShowItemAddedModal(false);
    setShowSuggestionModal(false);
  };

  const handleSingleItemWarningClose = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemWarningAddMore = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemWarningContinue = () => {
    setShowSingleItemWarningModal(false);

    const hasBothGiftAndCash =
      initialData.giftTypes?.includes('items') &&
      initialData.giftTypes?.includes('cash');

    if (hasBothGiftAndCash) {
      setShowFollowupModal(true);
    } else if (onNextStep) {
      // If only one gift type, proceed directly to next step
      console.log('Moving to next step with items:', allItems);
      onNextStep({
        giftItems: allItems,
      });
    }
  };

  const handleFollowupClose = () => {
    setShowFollowupModal(false);
  };

  const handleFollowupProceedToCashGift = () => {
    setShowFollowupModal(false);
    if (onNextStep) {
      console.log(
        'Proceeding to cash gifts from followup modal with items:',
        allItems
      );
      onNextStep({
        giftItems: allItems,
        addToQueue: true,
      });
    }
  };

  const handleFollowupContinueAddingItems = () => {
    setShowFollowupModal(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      const newItem: GiftItem = {
        id: Date.now(),
        image: imageUrl,
        name: '',
      };
      setSelectedItem(newItem);
    }
  };

  const handleNextPage = () => {
    if ((currentPage + 1) * itemsPerPage < allItems.length) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const incrementQuantity = () => setQuantity((prev) => prev + 1);
  const decrementQuantity = () => setQuantity((prev) => Math.max(0, prev - 1));
  return (
    <div className="px-4 md:px-0 md:pl-3.5 overflow-hidden">
      <div
        className={`flex  flex-col md:flex-row items-center md:items-start gap-5 mb-9 mt-11 transition-all duration-500 ${
          isBlurring ? ' justify-between w-full' : 'justify-center '
        }`}>
        <div
          className={`relative cursor-pointer ${
            isBlurring ? 'blur -translate-x-28 ' : ''
          }`}
          onClick={() => fileInputRef.current?.click()}>
          <img
            src={selectedItem?.image || img}
            alt="gift-img"
            className={`h-[399px] w-[281px] object-cover   ${
              isBlurring ? 'opacity-30 hidden md:block' : ''
            }`}
          />
        </div>
        {preview && <PreviewGiftItems onBack={handleBack} items={allItems} />}
        <div
          className={`max-w-[328px] w-full ${
            isBlurring ? 'blur translate-x-28' : ''
          }`}>
          <TextInput
            id="giftName"
            label="Gift Name"
            value={giftName}
            onChange={(e) => setGiftName(e.target.value)}
            placeholder="E.g iPhone 15"
            className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
          />
          <div className="my-4">
            <div className="flex items-center justify-end">
              <div className="flex  items-center border border-gray-300 rounded-full">
                <button
                  onClick={decrementQuantity}
                  className="border-r px-3 py-1 rounded-l-lg transition-colors"
                  disabled={quantity === 0}>
                  <Minus color="#E6E6E6" size={20} />
                </button>
                <span className="py-1  text-sm font-medium text-gray-900 px-3.5 text-center">
                  {quantity}
                </span>
                <button
                  onClick={incrementQuantity}
                  className="border-l rounded-r-lg transition-colors px-3 py-1">
                  <Add color="#000" size={20} />
                </button>
              </div>
            </div>
          </div>
          <div className="flex gap-4 my-4">
            <div className="flex-1">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Price
              </label>
              <div className="flex">
                <div className="relative flex-1">
                  <input
                    type="text"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                    placeholder="₦ Enter Amount"
                    className="w-full h-11 px-3.5 border border-r-0 border-gray-300 rounded-l-full text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0"
                  />
                </div>
                <div className="border border-l-0 border-gray-300 rounded-r-full px-3.5 flex items-center bg-white">
                  <span className="text-grey-500 italic mr-1">NGN</span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M19 9l-7 7-7-7"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* <div className="w-[74px]">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Qty
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(Number(e.target.value))}
                  min="1"
                  className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-normal text-grey-300 outline-0"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M19 9l-7 7-7-7"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div> */}
          </div>
          <div className="mb-4">
            <label className="block text-grey-500 font-medium text-sm mb-2">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your gift item"
              className="w-full p-3.5 border border-gray-300 rounded-2xl text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0 [&::-webkit-scrollbar]:hidden h-15 resize-none"></textarea>
          </div>
          <div className="mb-4">
            <label className="block text-grey-500 font-medium text-sm mb-2">
              Link to Item <span className="text-grey-300">(Optional)</span>
            </label>
            <div className="relative">
              <div className="absolute left-3.5 top-1/2 -translate-y-1/2 text-grey-50">
                https://
              </div>
              <input
                type="text"
                value={link}
                onChange={(e) => setLink(e.target.value)}
                placeholder="Paste link to item"
                className="w-full h-11 pl-[70px] pr-3.5 border border-gray-300 rounded-full text-base font-normal text-grey-50 placeholder:font-normal placeholder:text-grey-700 italic outline-0"
              />
            </div>
          </div>
          <div className="flex items-center gap-3 mb-4">
            <button
              onClick={() => setMostWanted(!mostWanted)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                mostWanted ? 'bg-primary-650' : 'bg-grey-850'
              }`}>
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-grey-350 shadow-[0px_1px_2px_0px_#0A0D120F,_0px_1px_3px_0px_#0A0D121A] transition-transform ${
                  mostWanted ? 'translate-x-4' : 'translate-x-1'
                }`}
              />
            </button>
            <span className="text-sm text-grey-500 font-medium">
              Set as Most wanted Item
            </span>
          </div>
          <Button
            variant="primary"
            size="md"
            className="bg-primary-250 h-7 text-primary"
            iconLeft={<AddCircle size="18" color="#4D55F2" variant="Bulk" />}
            onClick={handleAddToQueue}>
            Add to Queue{' '}
          </Button>

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleImageUpload}
          />
        </div>

        <button
          onClick={handleNextStep}
          className={`bg-primary-650  ${
            isBlurring ? 'hidden' : ''
          } text-white py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2`}>
          Continue
          <div className="bg-white/30 rounded-full p-0.5">
            <ArrowRight size="12" color="#fff" />
          </div>
        </button>
      </div>
      <div
        className={`flex-col  gap-2 md:gap-9  transition-all duration-500 ${
          isBlurring ? 'hidden' : 'flex'
        }`}>
        <div
          className={` items-center justify-center gap-2 md:gap-9  transition-all duration-500 ${
            isBlurring ? 'hidden' : 'flex'
          }`}>
          <div
            className="bg-primary-250 p-2 rounded-full cursor-pointer"
            onClick={handlePrevPage}>
            <ArrowCircleLeft2 color="#B8BBFA" size={20} variant="Bulk" />
          </div>
          <div className={`flex justify-center items-center gap-4 `}>
            {allItems
              .slice(
                currentPage * itemsPerPage,
                (currentPage + 1) * itemsPerPage
              )
              .map((item) => (
                <div
                  key={item.id}
                  className="flex-shrink-0 bg-[#f8f8f8] w-25 h-25 rounded-xl overflow-hidden border border-gray-200 cursor-pointer"
                  onClick={() => handleItemSelect(item)}>
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
          </div>
          <div
            className="bg-primary-750 p-2 rounded-full cursor-pointer"
            onClick={handleNextPage}>
            <ArrowCircleRight2 color="#fff" size={20} variant="Bulk" />
          </div>
        </div>
        <button
          type="button"
          onClick={handleContinue}
          disabled={allItems.length === 0}
          className={`mb-20 lg:ml-70 w-fit text-sm font-semibold border py-2 px-3.5 rounded-full mt-5 transition-colors ${
            allItems.length === 0
              ? 'text-gray-400 bg-gray-100 border-gray-300 cursor-not-allowed'
              : 'text-primary bg-white border-primary-950'
          }`}>
          View All
        </button>
      </div>

      {/* Item Added Modal */}
      {showItemAddedModal && lastAddedItem && (
        <ItemAddedModal
          itemName={lastAddedItem.name}
          onProceedToCashGift={handleProceedToCashGift}
          onContinueAddingItems={handleContinueAddingItems}
        />
      )}

      {/* Suggestion Modal */}
      {showSuggestionModal && lastAddedItem && (
        <SuggestionModal
          itemName={lastAddedItem.name}
          suggestedItem={
            lastAddedItem.name === 'iPhone 15 Pro' ? 'Earbuds' : 'Phone Case'
          }
          onProceedToAdd={handleContinueAddingItems}
          onAddOtherItems={handleContinueAddingItems}
          onClose={() => setShowSuggestionModal(false)}
        />
      )}

      {/* Single Item Warning Modal */}
      <SingleItemWarningModal
        isOpen={showSingleItemWarningModal}
        onClose={handleSingleItemWarningClose}
        onAddMore={handleSingleItemWarningAddMore}
        onContinueAnyway={handleSingleItemWarningContinue}
      />

      {/* Followup Modal */}
      <FollowupModal
        isOpen={showFollowupModal}
        onClose={handleFollowupClose}
        onProceedToCashGift={handleFollowupProceedToCashGift}
        onContinueAddingItems={handleFollowupContinueAddingItems}
      />
    </div>
  );
};
