/* eslint-disable @typescript-eslint/no-explicit-any */
import { Footer } from '../footer';
import { useEffect, useState } from 'react';
import { RegistryDetails } from './registry-details';
import { AddGiftItems } from './add-gift-items';
import { AddCashGift } from './add-cash-gift';
import {
  StepProgress,
  Step,
} from '../../../components/step-progress/step-progress';
import { PreviewAndCreate } from './PreviewAndCreate';
import { CashGiftPreview } from './CashGiftPreview';
import { CombinedPreview } from './CombinedPreview';
import { useEventStore } from '../../../lib/store/event';

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  bank?: string;
  accountNumber?: string;
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
  giftName?: string;
  price?: string;
  quantity?: number;
  description?: string;
  link?: string;
  giftItems?: GiftItem[];
  cashGifts?: CashGift[];
}

interface GiftItem {
  id: number;
  image: string;
  name: string;
  price?: string;
  quantity?: number;
  description?: string;
  link?: string;
}

interface CashGift {
  id: number;
  amount: string;
  description: string;
}

interface CreateGiftRegistryProps {
  onClose?: () => void;
}

export const CreateGiftRegistry = ({ onClose }: CreateGiftRegistryProps) => {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [registryData, setRegistryData] = useState<RegistryData>({
    giftItems: [],
    cashGifts: [],
  });
  const { selectedEvent } = useEventStore();

  const [steps, setSteps] = useState<Step[]>([
    { id: 1, name: 'Registry Details' },
    { id: 2, name: 'Add Gift Items' },
    { id: 3, name: 'Preview & Create' },
  ]);

  useEffect(() => {
    if (
      registryData.giftTypes?.includes('cash') &&
      registryData.giftTypes?.includes('items')
    ) {
      setSteps([
        { id: 1, name: 'Registry Details' },
        { id: 2, name: 'Add Gift Items' },
        { id: 3, name: 'Add Cash Gift' },
        { id: 4, name: 'Preview & Create' },
      ]);
    } else {
      const step2Name = registryData.giftTypes?.includes('cash')
        ? 'Add Cash Gift'
        : registryData.giftTypes?.includes('items')
        ? 'Add Gift Items'
        : 'Add Gift Items';

      setSteps([
        { id: 1, name: 'Registry Details' },
        { id: 2, name: step2Name },
        { id: 3, name: 'Preview & Create' },
      ]);
    }
  }, [registryData.giftTypes]);

  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    };
  }, []);

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };

  const handleNextStep = (data?: any) => {
    console.log('handleNextStep called with data:', data);

    if (data) {
      if (activeStep === 2) {
        if (data.giftItems) {
          setRegistryData((prev) => ({
            ...prev,
            giftItems: [...(prev.giftItems || []), ...data.giftItems],
          }));
          console.log('Added gift items:', data.giftItems);
        } else if (data.giftName) {
          const newItem: GiftItem = {
            id: Date.now(),
            name: data.giftName,
            price: data.price,
            quantity: data.quantity,
            description: data.description,
            link: data.link,
            image: data.image || registryData.giftItems?.[0]?.image || '',
          };

          setRegistryData((prev) => ({
            ...prev,
            giftItems: [...(prev.giftItems || []), newItem],
          }));
          console.log('Added single gift item:', newItem);
        }
      } else if (activeStep === 3) {
        if (data.cashGifts && data.cashGifts.length > 0) {
          setRegistryData((prev) => ({
            ...prev,
            cashGifts: data.cashGifts,
          }));
          console.log('Added cash gifts:', data.cashGifts);
        } else if (data.amount) {
          const newCashGift: CashGift = {
            id: Date.now(),
            amount: data.amount,
            description: data.description || '',
          };

          setRegistryData((prev) => ({
            ...prev,
            cashGifts: [...(prev.cashGifts || []), newCashGift],
          }));
          console.log('Added cash gift:', newCashGift);
        }
      } else if (activeStep === 1 && data.giftTypes) {
        setRegistryData((prev) => ({ ...prev, ...data }));
        console.log('Updated registry details:', data);
      } else {
        setRegistryData((prev) => ({ ...prev, ...data }));
        console.log('Updated registry with other data:', data);
      }
    }

    setCompletedSteps((prev) => [...new Set([...prev, activeStep])]);
    setActiveStep((prevStep) => prevStep + 1);
  };

  console.log(registryData.giftTypes, 'gifttypes');

  return (
    <div className="fixed inset-0 z-50 bg-white overflow-y-auto [&::-webkit-scrollbar]:hidden">
      <div className="min-h-screen">
        <div className="flex flex-col w-full font-rethink min-h-screen">
          <div
            className={`  px-4 md:px-0 right-0 z-50 max-w-[560px] w-full mx-auto`}>
            <div className="h-[48px] absolute top-[-50px] bg-transparent w-full max-w-[443px] blur-xl [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] group-scroll:shadow-none group-scroll:bg-white/10 group-scroll:backdrop-blur-md"></div>
            <div className="">
              <div className="flex justify-end pt-13 pb-5 ">
                <button
                  onClick={onClose}
                  className="px-6 py-3 cursor-pointer rounded-full bg-primary-250 text-primary font-medium">
                  Cancel
                </button>
              </div>
            </div>
          </div>

          <div className="max-w-[560px] w-full mx-auto px-4 md:px-0 ">
            <h1 className="text-[28px] font-semibold mt-7 md:ml-3.5">
              Create Gift Registry
            </h1>
            <p className="text-base text-grey-950 md:ml-3.5">
              Let's curate your guest list!
            </p>
          </div>
          <StepProgress
            steps={steps}
            activeStep={activeStep}
            completedSteps={completedSteps}
            onStepChange={handleStepChange}
          />

          {activeStep === 1 && (
            <RegistryDetails
              onNextStep={(data) => handleNextStep(data)}
              initialData={registryData}
            />
          )}

          {registryData.giftTypes?.includes('items') &&
            registryData.giftTypes?.includes('cash') && (
              <>
                {activeStep === 2 && (
                  <AddGiftItems
                    onNextStep={(data) => {
                      if (data.addToQueue) {
                        setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                        setActiveStep(3);
                      } else {
                        handleNextStep(data);
                      }
                    }}
                    initialData={registryData}
                  />
                )}
                {activeStep === 3 && (
                  <AddCashGift
                    onNextStep={(data) => {
                      if (data.proceedToPreview) {
                        setCompletedSteps((prev) => [...new Set([...prev, 3])]);
                        setActiveStep(4);
                      } else {
                        handleNextStep(data);
                      }
                    }}
                    initialData={registryData}
                  />
                )}

                {activeStep === 4 &&
                  (() => {
                    console.log(
                      'Rendering CombinedPreview with data:',
                      registryData
                    );
                    return (
                      <CombinedPreview
                        onClose={onClose}
                        initialData={registryData}
                      />
                    );
                  })()}
              </>
            )}

          {!(
            registryData.giftTypes?.includes('items') &&
            registryData.giftTypes?.includes('cash')
          ) && (
            <>
              {activeStep === 2 &&
                registryData.giftTypes?.includes('items') && (
                  <AddGiftItems
                    onNextStep={(data) => handleNextStep(data)}
                    initialData={registryData}
                  />
                )}
              {activeStep === 2 && registryData.giftTypes?.includes('cash') && (
                <AddCashGift
                  onNextStep={(data) => handleNextStep(data)}
                  initialData={registryData}
                />
              )}

              {activeStep === 3 &&
                (registryData.giftTypes?.includes('cash') ? (
                  <CashGiftPreview
                    onClose={onClose}
                    initialData={registryData}
                    eventId={selectedEvent?.id}
                  />
                ) : (
                  <PreviewAndCreate
                    initialData={registryData}
                    onClose={onClose}
                  />
                ))}
            </>
          )}
        </div>
        <Footer />
      </div>
    </div>
  );
};
