import {  useState } from 'react';
import { SetupGiftRegistry } from './setup-gift-registry';

export const SetGiftRegistry = ({ onClose }: { onClose?: () => void }) => {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const handleStepChange = (step: number) => {
    if (step > activeStep) {
      setCompletedSteps(prev => [...new Set([...prev, activeStep])]);
    }
    setActiveStep(step);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-white [&::-webkit-scrollbar]:hidden">
      <div className="flex flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] min-h-screen relative">
        <div
          className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
          style={{
            backgroundSize: '100% auto',
          }}
        />
        <div className="relative z-10 flex-1">
          <SetupGiftRegistry
            activeStep={activeStep}
            completedSteps={completedSteps}
            onStepChange={handleStepChange}
            onClose={onClose}
          />
        </div>
      </div>
    </div>
  );
};
