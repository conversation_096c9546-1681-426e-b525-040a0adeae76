import { PageTitle } from '../../components/helmet/helmet';
import { Footer } from './footer';
import { useEffect, useState, useRef } from 'react';
import { Modal } from '../../components/reuseables/prelaunch';
import { GuestCards } from './guestCards';
import { Onboarding } from './onboarding';
import gift from '../../assets/animations/gift.gif';
import gift1 from '../../assets/images/gift1.png';
import BudgetPriceBubbles from './budget';
import { modalVariants } from '../../components/reuseables/animations/animations';
import { motion } from 'motion/react';
// import { useMutation } from '@tanstack/react-query';
// import { AuthServices } from '../../lib/services/auth';
// import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../lib/store/auth';

export const Prelaunch = () => {
  const [isModalOpen1, setIsModalOpen1] = useState(true);
  const [isModalOpen2, setIsModalOpen2] = useState(false);
  const [isModalOpen3, setIsModalOpen3] = useState(false);
  const [isModalOpen4, setIsModalOpen4] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const authStore = useUserAuthStore((state) => state.userData);

  // const { mutate: completeOnboarding } = useMutation({
  //   mutationFn: () =>
  //     AuthServices.updateAuthenticatedUser({
  //       config: {
  //         completed_onboarding: true,
  //       },
  //       first_name: authStore?.first_name || '',
  //       last_name: authStore?.last_name || '',
  //     }),

  //   onError: () => {
  //     toast.error('Failed to complete onboarding. Please try again.');
  //   },
  // });


  useEffect(() => {
    const isAnyModalOpen =
      isModalOpen1 || isModalOpen2 || isModalOpen3 || isModalOpen4;

    if (isAnyModalOpen) {
      const scrollY = window.scrollY;

      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';
      if (modalRef.current) {
        const modalContent = modalRef.current.querySelector('.overflow-y-auto');
        if (modalContent) {
          modalContent.scrollTop = 0;
        }
      }
    } else {
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }

    return () => {
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
    };
  }, [isModalOpen1, isModalOpen2, isModalOpen3, isModalOpen4]);

  const closeAllModals = () => {
    setIsModalOpen1(false);
    setIsModalOpen2(false);
    setIsModalOpen3(false);
    setIsModalOpen4(false);
  };

  const handleFinishOnboarding = () => {
    // completeOnboarding();
    closeAllModals();
  };

  return (
    <div className="relative pt-20 px-3 md:px-0 font-rethink min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <motion.img
        src={gift}
        alt="background"
        className="absolute inset-0 w-full h-[469px] object-cover opacity-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.4 }}
        transition={{
          delay: 0.5,
          duration: 0.01,
          ease: 'linear',
        }}
      />
      <div className="relative z-10">
        <div className="flex justify-center pb-[108px]">
          <PageTitle title="Onboarding" />
          <Onboarding  />
        </div>
        <Footer />
      </div>
      <div ref={modalRef}>
        <Modal
          isOpen={isModalOpen1}
          onClose={closeAllModals}
          animateEntry={true}
          actionButton={() => {
            setIsModalOpen1(false);
            setIsModalOpen2(true);
          }}
          forwardActionText="Continue"
          title="Hi There"
          name={authStore?.first_name || ''}
          subtitle="HERE'S A SNEAK PEEK OF WHAT'S COMING!"
          hideButton={true}
          description={
            <p>
              Unlock early access to smart event <br /> planning, seamless
              gifting and effortless <br /> budgeting—exciting new features are{' '}
              <br /> just around the corner!
            </p>
          }
          moreStyle="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]"
          leftContent={<div className=""></div>}
        />
        <Modal
          isOpen={isModalOpen2}
          onClose={closeAllModals}
          actionButton={() => {
            setIsModalOpen2(false);
            setIsModalOpen3(true);
          }}
          actionButton2={() => {
            setIsModalOpen2(false);
            setIsModalOpen1(true);
          }}
          forwardActionText="Continue"
          secondModalAnimation={true}
          title="Guestlist "
          name="Manager"
          subtitle="Effortless Invites, Perfect Attendance"
          description={
            <p>
              Send invites, track RSVPs, and manage <br /> your guest list
              seamlessly—all in one place.
              <br /> No stress, just perfect planning!
            </p>
          }
          leftContent={<GuestCards />}
          leftContentMobile={true}
        />
        <Modal
          isOpen={isModalOpen3}
          onClose={closeAllModals}
          actionButton={() => {
            setIsModalOpen3(false);
            setIsModalOpen4(true);
          }}
          actionButton2={() => {
            setIsModalOpen3(false);
            setIsModalOpen2(true);
          }}
          forwardActionText="Continue"
          thirdModalAnimation={true}
          title="Gift"
          name="Registry"
          subtitle="Wish. Share. Receive."
          description={
            <p>
              Create the perfect wishlist, share it with <br /> loved ones, and
              receive gifts you truly want.
              <br /> No duplicates, no guesswork—just joy!
            </p>
          }
          leftContent={
            <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
              <img
                src={gift}
                alt="gift"
                className="h-full w-full object-cover opacity-40"
              />
              <motion.img
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={modalVariants}
                src={gift1}
                alt="gift decoration"
                className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
              />
            </div>
          }
          leftContentMobile={true}
        />
        <Modal
          isOpen={isModalOpen4}
          onClose={closeAllModals}
          actionButton={handleFinishOnboarding}
          actionButton2={() => {
            setIsModalOpen4(false);
            setIsModalOpen3(true);
          }}
          forwardActionText="Finish"
          thirdModalAnimation={true}
          title="Budget"
          name="Planner"
          subtitle="Plan Smart, Spend Right."
          description={
            <p>
              Stay in control of your event finances with <br /> smart budgeting
              tools. Plan every expense, track <br /> spending, and make every
              penny count!
            </p>
          }
          differentColor={true}
          leftContent={<BudgetPriceBubbles />}
        />
      </div>
    </div>
  );
};
