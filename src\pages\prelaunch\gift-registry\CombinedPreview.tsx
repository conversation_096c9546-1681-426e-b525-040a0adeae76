import { useState } from 'react';
import { ArrowCircleRight2, CloseCircle, Tag2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import { Success } from './success';

interface GiftItem {
  id: number;
  name: string;
  description?: string;
  price?: number | string;
  image: string;
  quantity?: number;
  link?: string;
}

interface CashGift {
  id: number;
  amount: string;
  description: string;
}

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  giftItems?: GiftItem[];
  cashGifts?: CashGift[];
  bank?: string;
  accountNumber?: string;
  accountName?: string;
  location?: string;
}

interface CombinedPreviewProps {
  initialData?: RegistryData;
  onClose?: () => void;
}

export const CombinedPreview = ({
  initialData = {},
  onClose = () => {},
}: CombinedPreviewProps) => {
  const [open, setOpen] = useState(false);

  // Debug logs
  console.log('CombinedPreview initialData:', initialData);
  console.log('Cash gifts:', initialData.cashGifts);
  console.log('Gift items:', initialData.giftItems);

  const accountDetails = {
    bank: initialData.bank || 'GTBank',
    accountNumber: initialData.accountNumber || '**********',
    accountName: initialData.accountName || 'ADE BOLUWATIFE',
    location:
      initialData.location || 'Lekki Conservation Center, Lekki, Lagos State',
  };

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0">
        <div className="max-w-[560px] mx-auto">
          <h1 className="text-[28px] font-semibold mt-5 italic mb-4">
            {initialData.registryTitle || "Oladele's birthday gifts"}
          </h1>

          <div className="pt-5 px-4 pb-4 rounded-xl bg-[linear-gradient(182.72deg,#FEF7F4_20.31%,#F5F6FE_97.2%)]">
            <div className="text-2xl font-bold mb-1.5">
              {accountDetails.accountNumber}
            </div>
            <div>
              <span className="text-cus-orange-700 font-medium text-sm">
                {accountDetails.bank}{' '}
              </span>
              <span className="text-grey-950 text-sm italic font-bold">
                • {accountDetails.accountName}
              </span>
            </div>
            <div className="flex items-center text-sm italic text-dark-blue-200 gap-2 mt-4.5">
              <Icon name="bus" />
              <span>{accountDetails.location}</span>
            </div>
          </div>

          {/* Cash Gifts Section */}
          {initialData.cashGifts && initialData.cashGifts.length > 0 && (
            <div className="mt-8">
              <div className="my-6 text-primary text-sm bg-primary-250 w-fit px-2.5 py-1 rounded-2xl italic font-bold">
                {initialData.cashGifts.length} Cash Gifts
              </div>

              <div className="grid grid-cols-2 gap-4">
                {initialData.cashGifts.map((cashGift) => (
                  <div
                    key={cashGift.id}
                    className="bg-[#F5F9FF] rounded-xl p-4 relative">
                    <button className="absolute top-2 right-2 bg-white rounded-full p-1">
                      <CloseCircle color="#9499F7" variant="Bulk" size={20} />
                    </button>
                    <p className="text-xl font-bold">₦{cashGift.amount}</p>
                    <p className="text-gray-500 text-sm mt-1">
                      {cashGift.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Gift Items Section */}
          {initialData.giftItems && initialData.giftItems.length > 0 && (
            <div className="mt-8">
              <div className="my-6 text-primary text-sm bg-primary-250 w-fit px-2.5 py-1 rounded-2xl italic font-bold">
                {initialData.giftItems.length} Gift Items
              </div>

              <div className="space-y-6">
                {initialData.giftItems.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center py-3 sm:py-0 flex-col md:flex-row border gap-4 border-grey-150 rounded-[14px]">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-[155px] h-full object-contain lg:rounded-l-[14px]"
                    />
                    <div className="flex-1 pr-2">
                      <div className="flex justify-between">
                        <h2 className="text-[22px] text-grey-750 font-medium">
                          {item.name}
                        </h2>
                        <CloseCircle size={28} variant="Bulk" color="#9499F7" />
                      </div>
                      {item.description && (
                        <p className="text-grey-100 my-1">{item.description}</p>
                      )}
                      {item.link && (
                        <div className="">
                          <span className="underline text-primary text-xs italic font-bold">
                            Link to Item
                          </span>
                        </div>
                      )}
                      <div className="mt-4 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                        <Tag2 size={12} variant="Bulk" color="#5925DC" />
                        <span className="text-perple text-sm font-medium">
                          ₦
                          {item.price
                            ? typeof item.price === 'string'
                              ? parseFloat(
                                  item.price.replace(/,/g, '')
                                ).toLocaleString()
                              : Number(item.price).toLocaleString()
                            : '0'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-center mt-8">
          <button
            type="button"
            onClick={() => setOpen(true)}
            className="bg-primary text-base font-semibold cursor-pointer text-white rounded-full py-2.5 px-4 flex items-center gap-2">
            Create Gift Registry
            <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
          </button>
        </div>
      </div>
      {open && <Success onClose={onClose} />}
    </>
  );
};
