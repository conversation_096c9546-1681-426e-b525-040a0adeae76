import { useNavigate } from 'react-router-dom';
import successGIF from '../../../../assets/animations/gift.gif';

export const SuccessPayment = () => {
  const navigate = useNavigate();
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pt-4 md:pt-0 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center relative">
        {/* Confetti Background Pattern */}
        <img
          src={successGIF}
          alt="gif"
          className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
        />

        <div className="relative w-full max-w-[450px] mx-auto">
          <div className="relative z-20 bg-white rounded-[20px] text-center shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            {/* Gift Illustration Background */}
            <div className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[274px] w-full flex items-center justify-center relative overflow-hidden">
              {/* Gift Box SVG */}
              <div className="relative">
                <img src="/success-svg.svg" alt="" />
              </div>
            </div>

            <div className="flex flex-col items-center text-center py-8 px-6 w-full">
              <h2 className="text-[28px] font-medium mb-2 text-[#000073] font-['Rethink_Sans'] tracking-[-0.035em]">
                You Just gifted
              </h2>
              <p className="text-[16px] text-[#808080] mb-6 font-['Rethink_Sans'] tracking-[-0.03em] leading-[1.6]">
                Olatunde successfully
              </p>

              <p className="text-[#808080] text-base mb-8 font-['Rethink_Sans']">
                You just gifted Olatunde an{' '}
                <span className="text-[#4D55F2] font-semibold">
                  Iphone 15 Pro
                </span>
              </p>

              <button
                onClick={() => navigate('/')}
                type="button"
                className="bg-[#343CD8] cursor-pointer text-base w-full max-w-[306px] text-white py-3 px-6 font-semibold rounded-full hover:bg-[#343CD8]/90 transition-colors shadow-[0px_1px_2px_0px_rgba(10,13,18,0.05)]">
                <span>Back to Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
