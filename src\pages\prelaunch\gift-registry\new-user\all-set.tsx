import { <PERSON>R<PERSON>, TickCircle } from 'iconsax-react';
import { useEffect, useState } from 'react';
import { CreateGiftRegistry } from '../create-gift-registry';

// interface AllSetProps {
//   onComplete: () => void;
// }

export const AllSet = () => {
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleGetStarted = () => {
    // onComplete();
    setIsGiftRegistryModalOpen(true);
    // Navigate to the gift registry page or dashboard
    // navigate('/prelaunch/gift-registry');
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="md:text-[40px] text-2xl font-medium">
          You’re All Set!
          <br /> start curating your gifts
        </h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-grin-50 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <TickCircle size="61" variant="Bulk" color="#3CC35C" />{' '}
          </div>

          <div className="-mt-12">
            <p>
              Congratulations, you’re done setting up your gift registry
              account, you can start curating your gifts
            </p>

            <button
              onClick={handleGetStarted}
              className="bg-primary-650 text-white py-2.5 px-4 mt-32 mb-0 rounded-full cursor-pointer flex items-center gap-2 ">
              Get Started
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
      {isGiftRegistryModalOpen && (
        <CreateGiftRegistry onClose={() => setIsGiftRegistryModalOpen(false)} />
      )}
    </div>
  );
};
