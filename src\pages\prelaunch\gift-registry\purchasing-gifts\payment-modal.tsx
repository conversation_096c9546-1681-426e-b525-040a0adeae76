import { useState } from "react";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { SuccessPayment } from "./success";

interface PaymentModalProps {
  onPayNow: () => void;
  onPayLater: () => void;
  onClose: () => void;
}

export const PaymentModal = ({
  onPayNow,
  onPayLater,
  onClose,
}: PaymentModalProps) => {
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handlePaymentSuccess = () => {
    setShowSuccess(true);
    onPayNow();
  };

  if (showSuccess) {
    return <SuccessPayment />;
  }

  if (showPaymentMethods) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 hover:text-grey-50 transition-colors cursor-pointer"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>

          {/* Payment Methods Content */}
          <div className="p-8 pt-16">
            <h2 className="text-[28px] font-semibold text-grey-50 mb-2 text-center">
              Select Payment Method
            </h2>
            <p className="text-base text-grey-250 mb-8 text-center">
              Please choose a payment option below
            </p>

            {/* Payment Options */}
            <div className="space-y-4">
              {/* Direct Debit */}
              <div
                onClick={() => {
                  setSelectedMethod("direct-debit");
                  handlePaymentSuccess();
                }}
                className={`border-2 group rounded-2xl p-4 cursor-pointer transition-colors relative ${
                  selectedMethod === "direct-debit"
                    ? "border-[#4D55F2]"
                    : "border-[#E6E6E6] hover:border-[#4D55F2]"
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className="w-24 h-24 flex items-center justify-center">
                    <svg
                      className="group-hover:text-[#4D55F2] text-[#A6AAF9]  "
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M64 26.307C64 25.8592 63.8221 25.4299 63.5055 25.1133L58.8676 20.4755C58.5655 20.0185 58.0477 19.7169 57.459 19.7169H8.22888V16.2801H24.7895H50.597H57.4594C58.3916 16.2801 59.1475 15.5241 59.1475 14.592C59.1475 13.6599 58.3915 12.9039 57.4594 12.9039H51.2962L38.8869 0.4945C38.5703 0.177875 38.1409 0 37.693 0C37.2451 0 36.816 0.177875 36.4992 0.4945L24.09 12.9039H6.54075C5.6085 12.9039 4.85262 13.6599 4.85262 14.592V32.1414L0.4945 36.4994C0.177875 36.8159 0 37.2454 0 37.693C0 38.1406 0.177875 38.57 0.4945 38.8867L4.85262 43.2449V49.408C4.85262 50.3403 5.60862 51.0961 6.54075 51.0961H12.7039L25.1133 63.5055C25.43 63.822 25.8592 64 26.307 64C26.7547 64 27.184 63.8221 27.5007 63.5055L39.9101 51.0961H57.4593C58.3915 51.0961 59.1474 50.3401 59.1474 49.408V31.8587L63.5055 27.5007C63.8223 27.1841 64 26.7546 64 26.307ZM37.693 4.07575L46.5214 12.9039H28.8649L37.693 4.07575ZM55.7711 23.0933V26.0115H8.22888V23.0933H55.7711ZM4.07575 37.693L4.85275 36.916V38.47L4.07575 37.693ZM26.307 59.9245L17.4788 51.0962H35.1351L26.307 59.9245ZM55.7711 47.72H39.2108H13.4032H8.229V42.5459V32.8409V29.3881H55.7713V31.1596V47.72H55.7711ZM59.1474 27.084V25.53L59.9244 26.307L59.1474 27.084Z"
                        fill="currentColor"
                      />
                      <path
                        d="M46.2881 32.922C45.3558 32.922 44.5999 33.678 44.5999 34.6101C44.5999 35.5423 45.3559 36.2983 46.2881 36.2983C47.9168 36.2983 49.2419 37.6235 49.2419 39.2521C49.2419 40.881 47.9167 42.2063 46.2881 42.2063C45.0979 42.2063 44.0292 41.4979 43.5654 40.4016C43.5543 40.3753 43.5408 40.3509 43.5287 40.3255C43.5886 39.9764 43.6216 39.6181 43.6216 39.2521C43.6216 35.7615 40.7818 32.9219 37.2912 32.9219C33.8006 32.9219 30.9609 35.7615 30.9609 39.2521C30.9609 42.7428 33.8006 45.5825 37.2912 45.5825C39.0477 45.5825 40.6389 44.8629 41.7869 43.7036C42.9533 44.884 44.5602 45.5825 46.2882 45.5825C49.7788 45.5825 52.6184 42.7428 52.6184 39.2521C52.6183 35.7616 49.7787 32.922 46.2881 32.922ZM37.2911 42.2063C35.6623 42.2063 34.3372 40.881 34.3372 39.2521C34.3372 37.6234 35.6624 36.2983 37.2911 36.2983C38.9199 36.2983 40.2452 37.6235 40.2452 39.2521C40.2452 40.8811 38.9201 42.2063 37.2911 42.2063Z"
                        fill="currentColor"
                      />
                      <path
                        d="M26.0787 43.504H13.0709C12.1387 43.504 11.3828 42.748 11.3828 41.8158V36.6901C11.3828 35.7578 12.1388 35.002 13.0709 35.002H26.0787C27.0109 35.002 27.7668 35.758 27.7668 36.6901C27.7668 37.6222 27.0108 38.3782 26.0787 38.3782H14.7592V40.1276H26.0787C27.0109 40.1276 27.7668 40.8836 27.7668 41.8157C27.7668 42.7478 27.0109 43.504 26.0787 43.504Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      DIRECT DEBIT
                    </h3>
                    <p className="text-sm text-grey-500">
                      Automatically withdraws funds from your account
                    </p>
                  </div>
                  {selectedMethod === "direct-debit" && (
                    <div className="absolute top-4 right-4">
                      <div className="w-6 h-6 bg-[#4D55F2] rounded-full flex items-center justify-center">
                        <svg
                          width="12"
                          height="9"
                          viewBox="0 0 12 9"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1 4.5L4.5 8L11 1"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Bank Transfer */}
              <div
                onClick={() => {
                  setSelectedMethod("bank-transfer");
                  handlePaymentSuccess();
                }}
                className={`border-2 rounded-2xl p-4 cursor-pointer transition-colors group relative ${
                  selectedMethod === "bank-transfer"
                    ? "border-[#4D55F2]"
                    : "border-[#E6E6E6] hover:border-[#4D55F2]"
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className="w-24 h-24 flex items-center justify-center">
                    <svg
                      className="group-hover:text-[#4D55F2] text-[#A6AAF9]  "
                      width="64"
                      height="44"
                      viewBox="0 0 64 44"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M50.1753 36.7606H13.4998C12.9647 36.7606 12.9647 36.7606 10.2093 35.0726C9.04344 34.3581 7.88431 33.644 7.88431 33.644C7.35894 33.3202 7.03919 32.7475 7.03919 32.1303V27.0938C7.03919 26.1121 7.83506 25.3161 8.81694 25.3161C8.82944 25.3161 8.84881 25.3165 8.86606 25.3168C10.6881 25.3076 12.1674 23.8226 12.1674 21.9985C12.1674 20.175 10.6891 18.6903 8.86769 18.6801C7.86319 18.7103 7.03906 17.9016 7.03906 16.903V12.1897C7.03906 11.5843 7.34719 11.0205 7.85669 10.6936C7.85669 10.6936 9.09556 9.89833 10.3414 9.10333C13.2669 7.23633 13.2669 7.23633 13.8198 7.23633H50.4953C51.0304 7.23633 51.0304 7.23633 53.7858 8.92433C54.9517 9.63883 56.1108 10.353 56.1108 10.353C56.6362 10.6767 56.9559 11.2495 56.9559 11.8666V16.9031C56.9559 17.8848 56.1601 18.6808 55.1782 18.6808C55.1657 18.6808 55.1463 18.6805 55.1291 18.6801C53.3071 18.6893 51.8277 20.1743 51.8277 21.9985C51.8277 23.822 53.3061 25.3066 55.1274 25.3168C56.1329 25.2868 56.9561 26.0953 56.9561 27.094V31.8072C56.9561 32.4126 56.6479 32.9765 56.1384 33.3033C56.1384 33.3033 54.8996 34.0986 53.6537 34.8936C50.7282 36.7606 50.7282 36.7606 50.1753 36.7606ZM13.9783 33.2051H49.6789C50.3644 32.7753 51.8781 31.8105 53.4003 30.8353V28.6478C50.4528 27.8735 48.2722 25.1856 48.2722 21.9986C48.2722 18.8116 50.4528 16.1237 53.4003 15.3493V12.86C52.0163 12.0087 50.6522 11.1741 50.0168 10.7921H14.3162C13.6307 11.2218 12.1171 12.1867 10.5948 13.1618V15.3493C13.5423 16.1237 15.7229 18.8116 15.7229 21.9986C15.7229 25.1856 13.5423 27.8735 10.5948 28.6478V31.1372C11.9787 31.9883 13.3429 32.8232 13.9783 33.2051Z"
                        fill="currentColor"
                      />
                      <path
                        d="M62.2223 43.6883H1.77775C0.795875 43.6883 0 42.8922 0 41.9105V2.0883C0 1.10655 0.795875 0.310547 1.77775 0.310547H62.2223C63.2041 0.310547 64 1.10667 64 2.0883C64 3.06992 63.2041 3.86605 62.2223 3.86605H3.5555V40.1327H60.4444V16.8439C60.4444 15.8622 61.2402 15.0662 62.2221 15.0662C63.204 15.0662 64 15.8622 64 16.8439V41.9105C64 42.8922 63.2041 43.6883 62.2223 43.6883Z"
                        fill="currentColor"
                      />
                      <path
                        d="M35.6863 21.6094C35.1499 21.2384 34.186 20.8533 32.8185 20.464V16.4409C33.3799 16.5335 33.8055 16.805 34.0851 17.249C34.2771 17.5615 34.3951 17.9342 34.436 18.3564C34.4536 18.5387 34.6069 18.6778 34.79 18.6778H36.4606C36.5566 18.6778 36.6484 18.639 36.7153 18.5704C36.7821 18.5018 36.8185 18.409 36.816 18.3132C36.7858 17.1413 36.3902 16.1773 35.6405 15.4483C34.9672 14.7935 34.0188 14.3937 32.8183 14.2574V13.3223C32.8183 13.126 32.659 12.9668 32.4627 12.9668H31.5491C31.3529 12.9668 31.1936 13.126 31.1936 13.3223V14.2588C29.9773 14.3414 29 14.7763 28.2849 15.5539C27.5143 16.393 27.1235 17.3582 27.1235 18.423C27.1235 19.6155 27.497 20.5635 28.232 21.2392C28.8848 21.8479 29.8801 22.2999 31.1938 22.5848V27.1295C30.4395 26.9973 29.9273 26.6707 29.6345 26.1379C29.4978 25.8882 29.3213 25.3787 29.2569 24.37C29.245 24.1828 29.0896 24.0373 28.902 24.0373H27.2149C27.0186 24.0373 26.8594 24.1965 26.8594 24.3928C26.8594 25.576 27.0501 26.49 27.4416 27.1849C28.1285 28.4189 29.3898 29.1365 31.1936 29.3208V30.6783C31.1936 30.8745 31.3529 31.0338 31.5491 31.0338H32.4627C32.659 31.0338 32.8183 30.8745 32.8183 30.6783V29.3077C33.8734 29.1617 34.7104 28.8929 35.3104 28.5068C36.5305 27.7165 37.144 26.393 37.1339 24.5753C37.1341 23.2725 36.6473 22.2749 35.6863 21.6094ZM32.8186 22.9978C33.4539 23.2017 33.7888 23.3817 33.9631 23.5052C34.4583 23.8559 34.6987 24.3579 34.6987 25.0399C34.6987 25.4844 34.6063 25.8674 34.4155 26.2122C34.1101 26.7674 33.5858 27.0896 32.8185 27.191V22.9978H32.8186ZM29.5591 18.3129C29.5591 17.8644 29.7129 17.438 30.0279 17.0122C30.2641 16.6967 30.6554 16.4984 31.1939 16.421V20.0959C30.7378 19.9619 30.3683 19.7748 30.0738 19.5302C29.7274 19.2365 29.5591 18.8383 29.5591 18.3129Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      BANK TRANSFER
                    </h3>
                    <p className="text-sm text-grey-500">
                      Manually send money from your bank account
                    </p>
                  </div>
                  {selectedMethod === "bank-transfer" && (
                    <div className="absolute top-4 right-4">
                      <div className="w-6 h-6 bg-[#4D55F2] rounded-full flex items-center justify-center">
                        <svg
                          width="12"
                          height="9"
                          viewBox="0 0 12 9"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1 4.5L4.5 8L11 1"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Debit Card */}
              <div
                onClick={() => {
                  setSelectedMethod("debit-card");
                  handlePaymentSuccess();
                }}
                className={`border-2 rounded-2xl p-4 cursor-pointer transition-colors group relative ${
                  selectedMethod === "debit-card"
                    ? "border-[#4D55F2]"
                    : "border-[#E6E6E6] hover:border-[#4D55F2]"
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className="w-24 h-24 flex items-center justify-center">
                    <svg
                      className="group-hover:text-[#4D55F2] text-[#A6AAF9]  "
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M61.5906 25.2797L61.3916 25.1856C60.8107 24.8968 60.208 24.6526 59.5902 24.4552C58.0464 24.0125 56.4669 23.5919 54.9391 23.1853C54.3607 23.0333 53.7822 22.8792 53.2041 22.7232C52.8542 22.6292 52.5042 22.5377 52.1141 22.4326L51.2952 22.2173L51.2977 20.1728C51.2977 19.4506 51.3034 18.7257 51.3063 18.0035C51.3174 16.4041 51.3285 14.7523 51.2977 13.1297C51.248 10.5927 49.7678 8.59779 47.3334 7.79833L47.1984 7.7514C46.8288 7.61362 46.446 7.51447 46.0558 7.45531L44.8189 7.3141C42.7135 7.07176 40.5377 6.82173 38.3916 6.65855C37.3146 6.57562 36.2057 6.57775 35.1322 6.58098C34.6119 6.58367 34.0947 6.58657 33.5744 6.57822C32.4538 6.56422 31.3333 6.53949 30.21 6.51445C28.309 6.47595 26.3594 6.43359 24.4161 6.43359C23.835 6.43359 23.2549 6.43728 22.6756 6.44464C20.7611 6.47533 18.8103 6.59416 16.9262 6.71048C15.7446 6.78238 14.5216 6.85993 13.318 6.91244C10.5333 7.04352 7.76759 7.44197 5.05912 8.10231C3.68604 8.46158 2.47208 9.26871 1.60952 10.3958C0.74696 11.5229 0.285103 12.9055 0.297103 14.3248C0.360919 17.5869 0.360936 20.9046 0.360936 24.1139C0.360936 25.4118 0.36267 26.7095 0.36626 28.007C0.368953 28.7623 0.35797 29.5179 0.349892 30.2732C0.330504 31.8392 0.31107 33.4578 0.402351 35.0392C0.615341 38.7468 1.42319 40.5368 5.64825 41.3338C5.97137 41.3947 6.30687 41.4389 6.63323 41.4804C6.84648 41.5073 7.0622 41.5358 7.27516 41.5689C7.74856 41.6408 8.2176 41.7184 8.72517 41.7986L9.6215 41.9454L9.6382 42.1638C9.64923 42.2968 9.66302 42.4128 9.67405 42.5151C9.70441 42.6969 9.71192 42.8822 9.69641 43.0658C9.46105 44.8615 10.2247 46.1206 12.0341 46.9163C12.5958 47.1624 13.1521 47.4199 13.7109 47.6772C15.2545 48.4369 16.8526 49.081 18.492 49.6029C23.7298 51.1135 29.106 52.4776 34.305 53.7974L35.1516 54.0127C39.7668 55.1832 44.3849 56.265 48.2889 57.1672C50.4579 57.6706 51.9713 57.4519 53.2 56.4644C54.1129 55.7432 54.9088 54.8854 55.5602 53.9217C56.224 52.8443 56.7607 51.6944 57.1594 50.4932C58.1358 47.7649 59.0963 44.9789 60.0259 42.2838C60.5646 40.7181 61.1069 39.1523 61.653 37.5859C62.7373 34.4703 63.3736 31.8556 63.6476 29.3544C63.856 27.4682 63.1069 25.9822 61.5906 25.2797ZM4.59861 12.3088C5.08328 11.805 5.88517 11.5783 6.31951 11.4897C8.76698 11.0264 11.2407 10.7141 13.7265 10.5545C21.2081 9.96212 28.5957 10.0066 36.7831 10.1146C39.8209 10.1561 42.7927 10.2446 45.7498 10.8893C47.4482 11.26 48.1042 12.0182 48.165 13.6896C48.19 14.3089 48.1842 14.9403 48.1789 15.6043C48.176 15.9253 48.1732 16.26 48.1732 16.6141V16.8963L47.8938 16.8909C45.589 16.8632 43.2993 16.8299 41.0237 16.7913C35.7528 16.7054 30.7781 16.6335 25.6841 16.6297C20.5847 16.6351 15.3831 16.7267 10.3556 16.815C8.10072 16.8568 5.8393 16.8946 3.57135 16.9286L3.36375 16.9313L3.30289 16.732C2.82494 15.1641 3.24824 13.7141 4.59861 12.3088ZM3.26495 19.8372H3.54179C5.32361 19.8263 7.0953 19.8124 8.85686 19.7957C12.8081 19.7626 16.8917 19.7296 20.893 19.746C24.3488 19.7654 27.8601 19.8455 31.255 19.9231C32.6821 19.9535 34.1076 19.9866 35.5326 20.0143L41.2435 20.1307C42.9309 20.1666 44.6167 20.2017 46.3012 20.2358C46.6031 20.2414 46.9014 20.2413 47.239 20.2384L48.0468 20.2358V22.43H47.7703C44.5527 22.4354 41.3354 22.4335 38.1197 22.4243C26.8059 22.4049 15.1103 22.3828 3.55579 22.9195L3.26522 22.9336L3.26495 19.8372ZM11.2197 38.8569C10.4727 38.8125 9.72279 38.7656 8.97313 38.7222C8.01157 38.6477 7.0553 38.5165 6.10921 38.3294L5.40103 38.205C4.38265 38.0335 3.77683 37.3859 3.69659 36.3872C3.67182 36.0772 3.64109 35.7679 3.61363 35.4574C3.54443 34.7242 3.47256 33.9662 3.45856 33.2136C3.43379 31.6556 3.43917 30.1199 3.44483 28.496C3.44752 27.8264 3.44841 27.1468 3.44752 26.4569V26.2021L3.69928 26.18C3.8708 26.1636 4.03395 26.147 4.18905 26.1263C4.50175 26.0855 4.81629 26.0606 5.13149 26.0514C5.91721 26.0431 6.70322 26.046 7.48599 26.046C8.36058 26.046 9.23192 26.046 10.1035 26.035C11.5783 26.0156 13.0503 25.9878 14.5251 25.9601C17.2366 25.9062 20.0451 25.8524 22.8062 25.8605C28.2847 25.8772 33.8545 25.9379 39.2414 25.9935C41.5877 26.0182 43.9332 26.0422 46.2778 26.0654C46.5822 26.068 46.8866 26.0681 47.2185 26.0681L47.9516 26.0654C47.9516 26.0654 48.0431 27.3296 48.0456 27.4764C48.0993 30.2514 48.2507 33.0601 47.6472 36.1892C47.2546 38.2257 46.7951 38.6711 44.7504 38.9783C44.6007 38.9972 44.4501 39.0074 44.2995 39.0088C42.0169 39.0335 39.7331 39.0623 37.4487 39.0944C32.1253 39.1665 26.6274 39.2411 21.2016 39.2411H20.4157C17.3484 39.236 14.2327 39.0451 11.2197 38.8569ZM55.8885 41.2308C55.8745 41.336 55.8688 41.4356 55.8614 41.5296C55.8565 41.7383 55.8257 41.9456 55.7703 42.1468C54.8681 44.9854 53.9799 47.7662 53.0556 50.558C52.8513 51.1409 52.5884 51.7022 52.27 52.2318C51.6997 53.2197 51.2435 53.6181 50.4411 53.6181C50.0735 53.6058 49.7088 53.5516 49.3539 53.4564C47.8901 53.0999 46.4263 52.7344 44.9625 52.3721C44.0603 52.1453 43.1574 51.9204 42.2536 51.6989C40.3527 51.2283 38.453 50.7619 36.5542 50.2987C32.1853 49.2275 27.6684 48.121 23.2343 46.9893C20.271 46.234 17.2192 45.4545 14.4358 44.0004C13.6194 43.5742 13.5642 43.4719 13.5806 42.4314L13.5863 42.1823L13.8353 42.1602C15.4443 42.035 17.0616 42.0593 18.6662 42.2321C18.8008 42.2432 18.9355 42.2655 19.0674 42.2876C19.2437 42.3207 19.4221 42.3412 19.6014 42.3485C20.9848 42.3789 22.3692 42.412 23.7545 42.4481C27.1883 42.5367 30.7383 42.625 34.2245 42.6002C37.8628 42.5733 41.6091 42.3762 45.3555 42.0218C47.5135 41.8172 49.0798 40.882 50.0066 39.2468L50.1174 39.0502L55.9127 41.0072L55.8885 41.2308ZM58.2901 34.8559L57.1775 38.1872L51.2288 36.5519L50.8788 36.4109C51.0954 35.5156 51.0462 34.7729 51.1873 33.9484C51.2731 33.4532 51.3535 31.9979 51.3535 31.9979L58.3508 34.593L58.2901 34.8559ZM59.3469 31.3554L59.0618 31.2945C56.6048 30.7718 54.1834 30.0926 51.8126 29.2608L51.63 29.1971V25.3844L59.8642 28.143L59.3469 31.3554Z"
                        fill="currentColor"
                      />
                      <path
                        d="M41.0004 28.6631C40.3501 28.4746 39.769 28.6494 39.1495 28.835C38.9666 28.8888 38.7813 28.9456 38.5958 28.9925C38.5102 29.0145 38.4216 29.0368 38.333 29.0616C37.8432 29.1946 37.2899 29.3466 36.8472 29.1613C36.285 28.9232 35.6805 28.8018 35.07 28.8044C34.182 28.8355 33.305 29.0132 32.4748 29.3301C31.8453 29.5491 31.2831 29.9267 30.842 30.4263C30.4009 30.926 30.0962 31.531 29.9569 32.1828C29.8059 32.8044 29.8174 33.4545 29.9903 34.0703C30.1635 34.6864 30.492 35.2473 30.9446 35.6994C32.4444 37.1688 34.276 37.4787 36.387 36.6235C37.0341 36.3459 37.7643 36.333 38.4208 36.5877C39.0267 36.7923 39.9233 37.0524 40.6953 36.9555C42.6339 36.715 44.0542 34.9856 44.071 32.8439V32.811C44.0829 31.8791 43.7887 30.9689 43.2341 30.2198C42.6798 29.4707 41.895 28.9239 41.0004 28.6631ZM35.1234 33.4914C35.0321 33.5738 34.9249 33.6368 34.8083 33.6764C34.6917 33.7159 34.5681 33.731 34.4456 33.7211C34.2905 33.7127 34.1363 33.6936 33.9838 33.6635C33.7423 33.6212 33.5234 33.4949 33.3659 33.307C33.2083 33.1192 33.1219 32.8816 33.1222 32.6364C33.1224 32.4831 33.1564 32.3318 33.2218 32.1932C33.287 32.0545 33.3823 31.9319 33.5002 31.8342C33.6184 31.7365 33.7566 31.6661 33.9052 31.6279C34.0536 31.5897 34.2087 31.5847 34.3592 31.6132L34.4817 31.66C34.7195 31.749 34.9263 31.9054 35.0768 32.11C35.2273 32.3147 35.3148 32.5587 35.3288 32.8123C35.3439 32.9336 35.3334 33.0566 35.2978 33.1736C35.2623 33.2905 35.2031 33.3988 35.1234 33.4914ZM40.6517 32.9574C40.4039 33.2405 40.0636 33.426 39.6915 33.4803C39.5305 33.4849 39.3713 33.4424 39.2337 33.3587C39.1177 33.2717 39.0229 33.1595 38.9569 33.0305C38.8907 32.9015 38.8549 32.7593 38.8519 32.6144C38.8298 32.4807 38.8379 32.3436 38.875 32.2133C38.9122 32.0829 38.9779 31.9623 39.0673 31.8604C39.1597 31.7677 39.272 31.6971 39.3955 31.654C39.5194 31.6108 39.6511 31.5963 39.7811 31.6114C39.9427 31.6048 40.1032 31.6404 40.247 31.7147C40.3905 31.7891 40.5122 31.8995 40.6002 32.0352C40.6883 32.171 40.7392 32.3273 40.7486 32.4887C40.7578 32.6502 40.7246 32.8112 40.6528 32.956L40.6517 32.9574Z"
                        fill="currentColor"
                      />
                      <path
                        d="M23.8706 13.8207C23.901 14.4516 23.461 14.6619 22.6613 14.8832L22.6034 14.8996C22.4681 14.9411 22.329 14.969 22.1882 14.9829C21.7926 14.9912 18.1624 15.113 17.4222 15.0134C17.1397 14.9746 16.7164 14.9191 16.5532 14.7532C16.2156 14.4101 16.257 13.9922 16.2156 13.6602C16.1658 13.2453 16.5255 12.987 16.902 12.9187C18.2273 12.678 19.3367 12.4513 20.7312 12.4513C21.3861 12.4483 22.0399 12.5047 22.6847 12.6198C23.4279 12.7525 23.838 13.1705 23.8706 13.8207Z"
                        fill="currentColor"
                      />
                      <path
                        d="M12.5817 14.1387C12.5844 14.4817 12.4932 15.0848 11.865 15.2453C11.2664 15.3848 10.6605 15.4912 10.0501 15.5637C9.48833 15.5854 8.92582 15.5763 8.365 15.5367C7.59573 15.4559 6.93169 15.3899 6.25666 15.2903C5.53719 15.1853 5.11659 14.8088 5.13329 14.2805C5.14999 13.7797 5.54018 13.4189 6.18211 13.3203C7.78667 13.0742 11.3033 12.9434 11.6603 13.0713L11.8706 13.1488C12.0851 13.2066 12.2729 13.3367 12.4025 13.517C12.5321 13.6974 12.5954 13.917 12.5817 14.1387Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      DEBIT CARD
                    </h3>
                    <p className="text-sm text-grey-500">
                      Pay instantly using your credit card details
                    </p>
                  </div>
                  {selectedMethod === "debit-card" && (
                    <div className="absolute top-4 right-4">
                      <div className="w-6 h-6 bg-[#4D55F2] rounded-full flex items-center justify-center">
                        <svg
                          width="12"
                          height="9"
                          viewBox="0 0 12 9"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1 4.5L4.5 8L11 1"
                            stroke="white"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
      <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative">
        {/* Money Illustration Background */}
        <div className="flex justify-center rounded-t-3xl mb-6 p-8 pt-[60px] bg-[#F5F6FE] h-[280px]">
          <div className="relative">
            <img src={stackMoney} alt="" className="w-[220px] h-auto" />
          </div>
        </div>

        {/* Modal Content */}
        <div className="px-8 pb-8 text-center">
          <h2 className="text-[28px] font-semibold text-grey-50 mb-4">
            Continue to Payment?
          </h2>

          <p className="text-base text-grey-250 mb-8 leading-relaxed max-w-[400px] mx-auto">
            Please note that payment for a gift reservation has to be made
            within 7 days after which the reservation would be cancelled, would
            you like to make payment now?
          </p>

          {/* Action Buttons */}
          <div className="space-y-3 flex gap-2 items-center">
            <button
              onClick={() => setShowPaymentMethods(true)}
              className="w-full h-[48px] bg-[#4D55F2] text-white rounded-full font-semibold text-base hover:bg-[#4D55F2]/90 transition-all"
            >
              Yes pay now
            </button>

            <button
              onClick={onPayLater}
              className="w-full h-[48px] bg-[#FFE5E5] text-[#ff4d4d] rounded-full font-semibold text-base hover:bg-[#FFE5E5]/80 transition-all"
            >
              No, pay later
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
