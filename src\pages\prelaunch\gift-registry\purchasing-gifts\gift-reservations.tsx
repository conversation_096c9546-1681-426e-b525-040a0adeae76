import { useState } from 'react';
import avatar from '../../../../assets/images/avatar.png';
import delivery from '../../../../assets/images/delivery.png';
import { Button } from '../../../../components/button/onboardingButton';
import { ArrowCircleRight2 } from 'iconsax-react';
import { SuccessPayment } from './success';
import { JumiaRedirect } from './redirect-jumia';
import { PurchaseModal } from './purchase-modal';

export const GiftReservations = () => {
  const [activeTab, setActiveTab] = useState<'purchase' | 'cash'>('purchase');
  const [payment, setPayment] = useState(false);
  const [showJumiaRedirect, setShowJumiaRedirect] = useState(false);
  const [purchaseModal, setPurchaseModal] = useState(false);
  
  const proceedToPayment = () => {
    setPurchaseModal(false);
    setShowJumiaRedirect(true);
  };

  return (
    <div>
      {!showJumiaRedirect ? (
        <div className="pt-4 px-1 md:px-4 pb-17 bg-[linear-gradient(179.05deg,_#FAFAFA_35.71%,_#FFFFFF_99.18%)] mx-1 md:mx-6 rounded-2xl mt-3.5">
          <div className="flex bg-grey-850 rounded-full justify-center p-0.5 mb-6">
            <button
              className={` py-1.5 rounded-full  w-full text-sm cursor-pointer focus:outline-none transition-colors duration-200 ${
                activeTab === 'purchase'
                  ? 'text-primary bg-white font-bold'
                  : 'text-grey-250'
              }`}
              style={
                activeTab === 'purchase'
                  ? { boxShadow: '0px 2px 8px rgba(77, 85, 242, 0.10)' }
                  : { background: 'transparent' }
              }
              onClick={() => setActiveTab('purchase')}>
              Purchase Item
            </button>
            {/* <button
              className={` py-1.5 rounded-full text-nowrap max-w-[164px]  w-full text-sm cursor-pointer font-medium focus:outline-none transition-colors duration-200 ${
                activeTab === 'cash'
                  ? 'text-primary bg-white font-bold'
                  : 'text-grey-250'
              }`}
              style={
                activeTab === 'cash'
                  ? { boxShadow: '0px 2px 8px rgba(77, 85, 242, 0.10)' }
                  : { background: 'transparent' }
              }
              onClick={() => setActiveTab('cash')}>
              Give Cash Equivalent
            </button> */}
          </div>

          {activeTab === 'purchase' && (
            <>
              <div className="bg-white rounded-[14px] p-3 ">
                <div className="flex items-center gap-2">
                  <img src={avatar} alt="icon" />
                  <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                    JUMIA
                  </p>
                </div>
                <p className="mt-4.5 text-sm font-medium text-grey-500 bg-grey-850 rounded-full truncate w-full px-2.5 py-0.5 max-w-[282px]">
                  http://www.jumia.com/jimike/iphone-15..
                </p>
              </div>
              <div className="bg-white border border-dashed border-grey-900 mt-4 rounded-[14px] p-3 ">
                <div className="flex items-center gap-2">
                  <img src={delivery} alt="icon" />
                  <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                    DELIVERY DETAILS
                  </p>
                </div>
                <p className="mt-4.5 text-sm font-medium text-dark-blue-100 max-w-[280px]">
                  Apartment 4B, Oakwood Estate, Victoria Island, Lagos Nigeria{' '}
                </p>
              </div>
              <Button
                variant="primary"
                size="md"
                // onClick={() => setShowJumiaRedirect(true)}
                onClick={() => setPurchaseModal(true)}
                className={`text-white mb-8 mt-5 bg-primary-650 `}
                iconRight={
                  <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
                }>
                Continue to Jumia
              </Button>
            </>
          )}

          {activeTab === 'cash' && (
            <>
              <div className="bg-white border border-dashed border-grey-900 mt-4 rounded-[14px] p-3 ">
                <div className="flex items-center gap-2">
                  <img src={delivery} alt="icon" />
                  <p className="text-xs font-medium text-grey-100 tracking-[0.12em]">
                    GIVE CASH EQUIVALENT{' '}
                  </p>
                </div>
                <p className="mt-4 text-xs text-grey-100">Amount</p>
                <p className="text-2xl font-bold">₦1,650,050.00</p>
                <p className="mt-6 text-xs italic text-grey-950 ">
                  Your payment is securely processed through a trusted gateway,
                  ensuring a smooth and safe gift contribution.{' '}
                </p>
              </div>
              <Button
                variant="primary"
                size="md"
                onClick={() => setPayment(true)}
                className={`text-white mb-20 mt-5 bg-primary-650 `}
                iconRight={
                  <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
                }>
                Continue to Payment
              </Button>
            </>
          )}

          {payment && <SuccessPayment />}
        </div>
      ) : (
        <JumiaRedirect />
      )}
      {purchaseModal && (
        <PurchaseModal
          onClose={() => setPurchaseModal(false)}
          onProceedToPayment={proceedToPayment}
        />
      )}
    </div>
  );
};
