import {
  AddCircle,
  ArrowCircleLeft2,
  ArrowCircleRight2,
  CloseCircle,
  // Moneys,
} from 'iconsax-react';
import { useState, useEffect } from 'react';
// import { TextInput } from "../../../components/inputs/text-input/text-input";
import { Button } from '../../../components/button/onboardingButton';
import avatar from '../../../assets/images/cash-avatar.png';
import stackMoney from '../../../assets/images/stack-money1.svg';
import { CrowdGiftingModal } from '../../../components/modals/CrowdGiftingModal';
import { SingleItemWarningModal } from '../../../components/modals/SingleItemWarningModal';

interface CashGiftData {
  amount?: string;
  description?: string;
  giftTypes?: string[];
  proceedToPreview?: boolean;
  cashGifts?: CashGift[];
}

interface CashGift {
  id: number;
  amount: string;
  description: string;
  image?: string;
}

interface AddCashGiftProps {
  onNextStep: (data: CashGiftData) => void;
  initialData?: CashGiftData;
}

export const AddCashGift = ({
  onNextStep,
  initialData = {},
}: AddCashGiftProps) => {
  const [amount, setAmount] = useState(initialData.amount || '');
  const [description, setDescription] = useState(initialData.description || '');
  const [enableCrowdGifting, setEnableCrowdGifting] = useState(false);
  const [showCrowdGiftingModal, setShowCrowdGiftingModal] = useState(false);
  const [showSingleItemWarningModal, setShowSingleItemWarningModal] =
    useState(false);
  const [preview, setPreview] = useState(false);
  const [cashGiftOptions] = useState<CashGift[]>([
    {
      id: 1,
      amount: '4,500,000',
      description: 'Round trip to Zanzibar and Portugal',
    },
    // { id: 2, amount: '50,000', description: "Let's curate your guest list!" },
    // { id: 3, amount: '50,000', description: "Let's curate your guest list!" },
  ]);
  const [allCashGifts, setAllCashGifts] = useState<CashGift[]>(cashGiftOptions);
  const [currentPage, setCurrentPage] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(
    window.innerWidth < 640 ? 1 : 3
  );

  useEffect(() => {
    const handleResize = () => {
      setItemsPerPage(window.innerWidth < 640 ? 1 : 3);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleAddToQueue = () => {
    if (!amount || !description) return;

    const numericAmount = parseFloat(amount.replace(/,/g, ''));

    if (numericAmount >= 200000) {
      setShowCrowdGiftingModal(true);
      return;
    }

    addToQueue();
  };

  const addToQueue = () => {
    const newCashGift: CashGift = {
      id: Date.now(),
      amount,
      description,
    };

    setAllCashGifts((prev) => [...prev, newCashGift]);

    setAmount('');
    setDescription('');
  };

  const handleContinue = () => {
    if (allCashGifts.length === 0) return;
    if (allCashGifts.length === 1) {
      setShowSingleItemWarningModal(true);
      return;
    }
    proceedToNextStep();
  };

  const proceedToNextStep = () => {
    onNextStep({
      cashGifts: allCashGifts,
      proceedToPreview: true,
    });
  };

  const handleNextPage = () => {
    if ((currentPage + 1) * itemsPerPage < allCashGifts.length) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleModalClose = () => {
    setShowCrowdGiftingModal(false);
  };

  const handleModalEnable = () => {
    setEnableCrowdGifting(true);
    addToQueue();
    setShowCrowdGiftingModal(false);
  };

  const handleModalContinue = () => {
    setEnableCrowdGifting(false);
    addToQueue();
    setShowCrowdGiftingModal(false);
  };

  const handleSingleItemModalClose = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemAddMore = () => {
    setShowSingleItemWarningModal(false);
  };

  const handleSingleItemContinueAnyway = () => {
    setShowSingleItemWarningModal(false);
    proceedToNextStep();
  };

  return (
    <>
      <div className="px-4 md:px-0 md:pl-3.5">
        <div className="max-w-[560px] w-full mx-auto mt-[43px]">
          {!preview ? (
            <>
              {' '}
              <div className="mb-6">
                <h2 className="text-sm text-grey-500 font-medium mb-4">
                  Cash gift Amount
                </h2>
                <div className="flex items-center relative">
                  <div className="flex-1 relative ">
                    <span className="absolute left-4 top-[53%] -translate-y-1/2 text-gray-500">
                      ₦
                    </span>
                    <input
                      type="number"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      placeholder="Enter Amount"
                      className="w-full h-12 pl-8 pr-4  border border-r-0 border-gray-300 rounded-l-full text-base font-normal outline-0"
                    />
                  </div>
                  <div className="border border-l-0 border-gray-300 rounded-r-full px-4 h-12 flex items-center bg-white">
                    <span className="text-grey-50 mr-1 italic">NGN</span>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M19 9l-7 7-7-7"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <button
                    onClick={handleContinue}
                    disabled={allCashGifts.length === 0}
                    className="ml-4 bg-primary-650 text-white py-3 px-6 rounded-full flex items-center gap-2 absolute -right-[0px] md:top-[unset] top-[-60px] md:-right-[180px]">
                    Continue
                  </button>
                </div>
              </div>
              <div className="mb-6">
                <h2 className="text-sm text-grey-500  font-medium mb-2">
                  Reason For Cashgift
                </h2>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="E.g Trip to Zanzibaar"
                  className="w-full h-12 px-4 border border-gray-300 rounded-full text-base font-normal outline-0"
                />
              </div>
              <Button
                variant="primary"
                size="md"
                className="bg-primary-250 h-7 text-primary !text-sm"
                iconLeft={
                  <AddCircle size="18" color="#4D55F2" variant="Bulk" />
                }
                onClick={handleAddToQueue}>
                Add to Queue
              </Button>
              <div className="mb-8 mt-6 relative">
                <p
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                  className="text-xs cursor-pointer text-primary-960 mb-2 underline underline-offset-2 font-medium w-fit">
                  Do you want to enable crowd gifting
                </p>
                {showTooltip && (
                  <div className="flex absolute left-0 top-[22px] z-50  bg-[#9499F7] text-white p-3 rounded-lg shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
                    <p className="text-sm leading-[1.667] tracking-[-0.03em] max-w-[322px]">
                      Crowd gifting allows your guests to be able to contribute
                      as a group towards this particular gift item
                    </p>
                    <CloseCircle
                      size="20"
                      color="white"
                      variant="Bulk"
                      className="cursor-pointer"
                      onClick={() => setShowTooltip(false)}
                    />
                  </div>
                )}
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setEnableCrowdGifting(!enableCrowdGifting)}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                      enableCrowdGifting ? 'bg-primary-650' : 'bg-grey-850'
                    }`}>
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-grey-350 shadow-[0px_1px_2px_0px_#0A0D120F,_0px_1px_3px_0px_#0A0D121A] transition-transform ${
                        enableCrowdGifting ? 'translate-x-4' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <span className="text-sm text-grey-500 font-medium">
                    Enable Crowd gifting
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-center lg:ml-20 gap-2 md:gap-9 ">
                <div
                  className="bg-primary-250 p-2 rounded-full cursor-pointer"
                  onClick={handlePrevPage}>
                  <ArrowCircleLeft2 color="#B8BBFA" size={20} variant="Bulk" />
                </div>
                <div className="flex justify-center items-center gap-4">
                  {allCashGifts
                    .slice(
                      currentPage * itemsPerPage,
                      (currentPage + 1) * itemsPerPage
                    )
                    .map((cashGift) => (
                      <div
                        key={cashGift.id}
                        className="bg-white rounded-2xl px-3 pt-3 pb-6 h-[164px] w-[173px] overflow-hidden shadow-[0px_12px_120px_0px_#5F5F5F0F] border border-[#F0F0F0]">
                        <div className="flex justify-between mb-6">
                          <img src={stackMoney} alt="" />

                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              opacity="0.4"
                              d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                              fill="#8288F6"
                            />
                            <path
                              d="M13.0594 11.9999L15.3594 9.69986C15.6494 9.40986 15.6494 8.92986 15.3594 8.63986C15.0694 8.34986 14.5894 8.34986 14.2994 8.63986L11.9994 10.9399L9.69937 8.63986C9.40937 8.34986 8.92937 8.34986 8.63938 8.63986C8.34938 8.92986 8.34938 9.40986 8.63938 9.69986L10.9394 11.9999L8.63938 14.2999C8.34938 14.5899 8.34938 15.0699 8.63938 15.3599C8.78938 15.5099 8.97937 15.5799 9.16937 15.5799C9.35937 15.5799 9.54937 15.5099 9.69937 15.3599L11.9994 13.0599L14.2994 15.3599C14.4494 15.5099 14.6394 15.5799 14.8294 15.5799C15.0194 15.5799 15.2094 15.5099 15.3594 15.3599C15.6494 15.0699 15.6494 14.5899 15.3594 14.2999L13.0594 11.9999Z"
                              fill="#8288F6"
                            />
                          </svg>
                        </div>
                        <p className="text-2xl font-medium">
                          ₦{cashGift.amount}
                        </p>
                        <p className="text-gray-500 text-xs mt-1 ">
                          {cashGift.description}
                        </p>
                      </div>
                    ))}
                </div>
                <div
                  className="bg-primary-750 p-2 rounded-full cursor-pointer"
                  onClick={handleNextPage}>
                  <ArrowCircleRight2 color="#fff" size={20} variant="Bulk" />
                </div>
              </div>
              <button
                type="button"
                onClick={() => setPreview(true)}
                disabled={allCashGifts.length === 0}
                className={`mb-20 text-sm font-semibold border py-2 px-3.5 rounded-full mt-5 transition-colors ${
                  allCashGifts.length === 0
                    ? 'text-gray-400 bg-gray-100 border-gray-300 cursor-not-allowed'
                    : 'text-primary bg-white border-primary-950'
                }`}>
                View All
              </button>
            </>
          ) : (
            <div>
              <div className="flex items-center mb-4">
                <button
                  onClick={() => setPreview(false)}
                  className="flex items-center cursor-pointer gap-2 italic text-primary-750 font-medium">
                  <ArrowCircleLeft2 color="#5F66F3" variant="Bulk" size={20} />
                  Back
                </button>
              </div>

              <div className="space-y-4">
                {cashGiftOptions.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center rounded-2xl p-2.5 shadow-[0px_12px_120px_0px_#5F5F5F0F] border border-grey-150">
                    <img
                      src={avatar}
                      alt="cash-gift"
                      className="object-contain w-10 h-10 rounded-full mr-4"
                    />
                    <div className="flex items-start justify-between w-full">
                      <div>
                        <h3 className="font-semibold text-sm text-dark-blue-200">
                          {item.amount}
                        </h3>
                        <p className="text-grey-650 text-xs">
                          N {item.description} •{' '}
                          <span className="italic underline text-cus-orange-100">
                            Link to Cash Gift
                          </span>
                        </p>
                      </div>
                      <button>
                        <CloseCircle color="#9499F7" variant="Bulk" size={20} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Crowd Gifting Modal */}
      <CrowdGiftingModal
        isOpen={showCrowdGiftingModal}
        onClose={handleModalClose}
        onEnable={handleModalEnable}
        onContinue={handleModalContinue}
      />

      {/* Single Item Warning Modal */}
      <SingleItemWarningModal
        isOpen={showSingleItemWarningModal}
        onClose={handleSingleItemModalClose}
        onAddMore={handleSingleItemAddMore}
        onContinueAnyway={handleSingleItemContinueAnyway}
      />
    </>
  );
};
