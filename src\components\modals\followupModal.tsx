import React, { useEffect } from 'react';
import { CloseCircle } from 'iconsax-react';
import box from '../../assets/images/gift-item-box.png';

interface FollowupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceedToCashGift: () => void;
  onContinueAddingItems: () => void;
}

export const FollowupModal: React.FC<FollowupModalProps> = ({
  isOpen,
  onClose,
  onProceedToCashGift,
  onContinueAddingItems,
}) => {
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black/20 bg-opacity-50"
        onClick={onClose}
      />

      <div className="relative bg-white mx-3 md:mx-0 rounded-[20px] max-w-[488px] w-full shadow-2xl">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 transition-colors"
          aria-label="Close modal">
          <CloseCircle size="40" color="#634C42" variant="Bulk" />
        </button>

        {/* Money Illustration Background */}
        {/* <div className="flex justify-center rounded-t-[20px] mb-6 p-8 pt-[60px] bg-[#F5F6FE] h-[280px]">
          <div className="relative">
            <img src={wallet} alt="wallet" className="w-[220px] h-auto" />
          </div>
        </div> */}
        <div
          className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] overflow-hidden"
          style={{
            clipPath:
              'polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)',
          }}>
          <div className="flex justify-center items-center">
            <img src={box} alt="wallet"  />
          </div>
        </div>

        <div className="flex flex-col items-center text-center px-4 w-full pb-10 mt-5">
          <h2 className="text-2xl md:text-[28px] font-medium my-2 text-dark-blue">
            Item(s) added <br />{' '}
            <span className="text-[18px] md:text-[26px] text-grey-250">
              successfully
            </span>
          </h2>
          <p className="text-grey-250 text-base mb-14 w-full">
            Are you done curating gift items for your gift registry list?
            <br /> How would you like to proceed?{' '}
          </p>

          <div className="flex gap-3   ">
            <button
              type="button"
              onClick={onProceedToCashGift}
              className="bg-primary cursor-pointer text-base text-nowrap text-white flex items-center justify-center py-3 px-4 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors w-full">
              <span>Proceed to Cash Gift</span>
            </button>
            <button
              type="button"
              onClick={onContinueAddingItems}
              className="border border-[#4D55F2] text-base text-nowrap   text-primary-650 font-semibold py-3 px-4 rounded-full transition-colors w-full">
              <span>Continue Adding Items</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
