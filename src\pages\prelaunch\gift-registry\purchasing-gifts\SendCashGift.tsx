import React, { useState } from "react";

interface SendCashGiftProps {
  onContinue: () => void;
}

export const SendCashGift: React.FC<SendCashGiftProps> = ({ onContinue }) => {
  const [giftAmount, setGiftAmount] = useState("");

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    setGiftAmount(value);
  };
  const formatAmount = (amount: string) => {
    if (!amount) return "₦0.00";
    return `₦${new Intl.NumberFormat("en-NG").format(parseInt(amount))}.00`;
  };

  return (
    <div className="w-full flex flex-col pl-2 md:pl-6 items-start pt-8 font-rethink">
      {/* Section Title */}
      <div className="font-medium text-[18px] tracking-[-0.04em] text-[#090909] mb-6 text-left">
        How would you like to gift Olatunde
      </div>
      {/* Card */}
      <div className="bg-gradient-to-b from-[#FAFAFA]  p-5 rounded-[16px] to-[#FFFFFF]">
        <div className="w-full border border-[#E6E6E6] bg-white rounded-[16px]  border-dashed  shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] p-6 flex flex-col gap-4">
          {/* Header Row */}
          <div className="flex items-center gap-2 mb-2">
            <span className="w-6 h-6 flex items-center justify-center rounded-full bg-[#E6E6E6]">
              {/* Truck-tick SVG */}
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M10.0026 1.9987V7.9987C10.0026 8.73203 9.4026 9.33203 8.66927 9.33203H1.33594V5.07869C1.8226 5.65869 2.56929 6.0187 3.39596 5.9987C4.06929 5.98536 4.67594 5.72537 5.12927 5.29203C5.33594 5.1187 5.50929 4.89869 5.64262 4.65869C5.88262 4.25202 6.01594 3.77201 6.0026 3.27201C5.9826 2.49201 5.63595 1.80536 5.09595 1.33203H9.33594C9.7026 1.33203 10.0026 1.63203 10.0026 1.9987Z"
                  fill="#292D32"
                />
                <path
                  d="M14.6693 9.33203V11.332C14.6693 12.4387 13.7759 13.332 12.6693 13.332H12.0026C12.0026 12.5987 11.4026 11.9987 10.6693 11.9987C9.93594 11.9987 9.33594 12.5987 9.33594 13.332H6.66927C6.66927 12.5987 6.06927 11.9987 5.33594 11.9987C4.6026 11.9987 4.0026 12.5987 4.0026 13.332H3.33594C2.22927 13.332 1.33594 12.4387 1.33594 11.332V9.33203H8.66927C9.4026 9.33203 10.0026 8.73203 10.0026 7.9987V3.33203H11.2293C11.7093 3.33203 12.1493 3.59204 12.3893 4.00537L13.5293 5.9987H12.6693C12.3026 5.9987 12.0026 6.2987 12.0026 6.66536V8.66536C12.0026 9.03203 12.3026 9.33203 12.6693 9.33203H14.6693Z"
                  fill="#292D32"
                />
                <path
                  opacity="0.4"
                  d="M5.33333 14.6667C6.06971 14.6667 6.66667 14.0697 6.66667 13.3333C6.66667 12.597 6.06971 12 5.33333 12C4.59695 12 4 12.597 4 13.3333C4 14.0697 4.59695 14.6667 5.33333 14.6667Z"
                  fill="#292D32"
                />
                <path
                  opacity="0.4"
                  d="M10.6693 14.6667C11.4057 14.6667 12.0026 14.0697 12.0026 13.3333C12.0026 12.597 11.4057 12 10.6693 12C9.93289 12 9.33594 12.597 9.33594 13.3333C9.33594 14.0697 9.93289 14.6667 10.6693 14.6667Z"
                  fill="#292D32"
                />
                <path
                  opacity="0.4"
                  d="M14.6667 8.35335V9.33333H12.6667C12.3 9.33333 12 9.03333 12 8.66667V6.66667C12 6.3 12.3 6 12.6667 6H13.5267L14.4933 7.69332C14.6067 7.89332 14.6667 8.12002 14.6667 8.35335Z"
                  fill="#292D32"
                />
                <path
                  d="M5.0915 1.33514C4.6115 0.90847 3.97148 0.655137 3.27148 0.66847C2.64481 0.681804 2.07149 0.915132 1.61816 1.28847C1.01816 1.78847 0.644828 2.55515 0.664828 3.39515C0.678161 3.89515 0.824831 4.36181 1.08483 4.75514C1.15816 4.86847 1.23816 4.9818 1.33149 5.0818C1.81816 5.6618 2.56485 6.0218 3.39151 6.0018C4.06485 5.98847 4.6715 5.72847 5.12483 5.29514C5.3315 5.12181 5.50484 4.9018 5.63818 4.6618C5.87818 4.25513 6.01149 3.77512 5.99816 3.27512C5.97816 2.49512 5.6315 1.80847 5.0915 1.33514ZM4.70483 3.01515L3.31148 4.36179C3.21148 4.45512 3.0915 4.5018 2.96484 4.5018C2.83817 4.5018 2.71816 4.45512 2.61816 4.36179L1.94482 3.72181C1.74482 3.52848 1.73818 3.21515 1.93151 3.01515C2.12484 2.81515 2.43818 2.80847 2.63818 3.0018L2.96484 3.31512L4.01151 2.30181C4.21151 2.10848 4.52484 2.11512 4.71817 2.31512C4.91151 2.50845 4.90483 2.82182 4.70483 3.01515Z"
                  fill="#292D32"
                />
              </svg>
            </span>
            <span className="font-medium text-[12px] text-[#8E8E93] tracking-[0.12em] uppercase">
              SEND CASHGIFT
            </span>
          </div>

          <div className="flex flex-col gap-1 mb-2">
            <span className="font-normal text-[12px] text-[#8E8E93] tracking-[-0.02em]">
              Amount
            </span>
            <span className="font-bold text-[24px] text-[#090909] tracking-[-0.025em]">
              {formatAmount(giftAmount)}
            </span>
          </div>

          {/* Description */}
          <div className="mb-2">
            <span className="font-normal text-[14px] text-[#666] tracking-[-0.03em] leading-[1.6] italic">
              Your payment is securely processed through a trusted gateway,
              ensuring a smooth and safe gift contribution.
            </span>
          </div>
          {/* Amount Section */}

          {/* Input Field */}
          <div className="mb-3">
            <label
              className="font-medium text-[14px] text-[#414651] mb-1 block"
              htmlFor="gift-amount-input"
            >
              Gift Amount
            </label>
            <div className="flex items-center gap-2 relative">
              <input
                id="gift-amount-input"
                type="text"
                value={giftAmount}
                onChange={handleAmountChange}
                placeholder="Input amount you want to gift"
                className="w-full h-12 rounded-full border border-[#D5D7DA] px-4 text-[16px] text-[#717680] outline-none mt-0.5 font-rethink placeholder-[#717680]"
                autoComplete="off"
                inputMode="numeric"
              />
              <svg
                width="20"
                className="absolute right-4"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.99902 12.2422C10.473 12.2422 10.8574 12.6266 10.8574 13.1006C10.8574 13.5746 10.473 13.9589 9.99902 13.959C9.525 13.959 9.14066 13.5746 9.14062 13.1006C9.14062 12.6265 9.52498 12.2422 9.99902 12.2422Z"
                  fill="#DBDDFC"
                  stroke="#B8BBFA"
                />
                <path
                  d="M9.99707 2.16797C12.2294 2.16797 13.3618 2.87044 13.9648 3.72852C14.5933 4.62287 14.7305 5.79757 14.7305 6.90137V8.38477L15.1689 8.43945C16.2624 8.57617 16.875 8.90569 17.2451 9.43848C17.6369 10.0025 17.831 10.8933 17.8311 12.3262V13.876C17.8311 15.5667 17.5648 16.4977 17.0293 17.0332C16.4938 17.5687 15.5628 17.835 13.8721 17.835H6.12207C4.43171 17.8349 3.50135 17.5686 2.96582 17.0332C2.43028 16.4977 2.16406 15.5667 2.16406 13.876V12.3262C2.16407 10.8933 2.35824 10.0025 2.75 9.43848C3.12016 8.90573 3.73268 8.57614 4.82617 8.43945L5.26367 8.38477V6.90137C5.26367 5.7975 5.40175 4.62289 6.03027 3.72852C6.63333 2.87048 7.76496 2.16804 9.99707 2.16797ZM9.99707 10.085C8.33882 10.0851 6.98047 11.4328 6.98047 13.1016C6.98061 14.7597 8.32842 16.118 9.99707 16.1182C11.6648 16.1182 13.0135 14.7693 13.0137 13.1016C13.0137 11.4421 11.6565 10.085 9.99707 10.085ZM9.99707 2.33496C8.241 2.33501 7.04546 2.75745 6.31445 3.63184C5.60067 4.48585 5.43066 5.65798 5.43066 6.90137V8.36816H5.62207V8.37598H14.5645V6.90137C14.5645 5.65783 14.3937 4.48587 13.6797 3.63184C12.9486 2.75741 11.7533 2.33496 9.99707 2.33496Z"
                  fill="#DBDDFC"
                  stroke="#B8BBFA"
                />
              </svg>
            </div>
          </div>
        </div>
        {/* Reserve Gift Button */}
        <button
          onClick={onContinue}
          className="flex mt-5 items-center gap-2 bg-[#4D55F2] text-white font-rethink font-semibold text-[14px] rounded-full px-6 py-3  shadow-sm hover:bg-[#343CD8] transition-all w-fit self-start"
        >
          Reserve Gift
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              opacity="0.4"
              d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
              fill="white"
            />
            <path
              d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
              fill="white"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
