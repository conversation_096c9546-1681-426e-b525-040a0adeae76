import React, { useState } from "react";
import { <PERSON>Left, ArrowRight, Eye, EyeSlash } from "iconsax-react";
// import { useNavigate } from "react-router-dom";

interface WithdrawalFlowProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
}

export const WithdrawalFlow: React.FC<WithdrawalFlowProps> = ({
  isOpen,
  onClose,
  // amount,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [pin, setPin] = useState(["", "", "", ""]);
  const [confirmPin, setConfirmPin] = useState(["", "", "", ""]);
  const [showPin, setShowPin] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState("");
  const [bankDetails, setBankDetails] = useState({
    bank: "",
    accountNumber: "",
  });
  const [isVerified, setIsVerified] = useState(false);
  // const navigate = useNavigate();

  if (!isOpen) return null;

  const handlePinChange = (index: number, value: string, isConfirm = false) => {
    if (value.length > 1) return;

    const newPin = isConfirm ? [...confirmPin] : [...pin];
    newPin[index] = value;

    if (isConfirm) {
      setConfirmPin(newPin);
    } else {
      setPin(newPin);
    }

    // Auto focus next input
    if (value && index < 3) {
      const nextInput = document.getElementById(
        `${isConfirm ? "confirm-" : ""}pin-${index + 1}`
      );
      nextInput?.focus();
    }
  };

  const isPinComplete =
    pin.join("").length === 4 &&
    confirmPin.join("").length === 4 &&
    pin.join("") === confirmPin.join("");

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 1
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          1
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 1 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Create Transaction Pin
        </span>
      </div>
      <ArrowRight size="16" color="#E5E7EB" className="mx-4" />
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 2
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          2
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 2 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Select Account
        </span>
      </div>
      <ArrowRight size="16" color="#E5E7EB" className="mx-4" />
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 3
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          3
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 3 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Authenticate
        </span>
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="text-center">
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-8">
        Create and confirm a 4-digit transaction pin that you will remember
      </p>

      {/* PIN Input */}
      <div className="mb-6">
        <div className="flex justify-center gap-3 mb-6">
          {pin.map((digit, index) => (
            <input
              key={index}
              id={`pin-${index}`}
              type={showPin ? "text" : "password"}
              value={digit}
              onChange={(e) => handlePinChange(index, e.target.value)}
              className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
              maxLength={1}
            />
          ))}
        </div>

        <p className="text-sm text-gray-600 mb-4">Confirm PIN</p>
        <div className="flex justify-center gap-3 mb-6">
          {confirmPin.map((digit, index) => (
            <input
              key={index}
              id={`confirm-pin-${index}`}
              type={showPin ? "text" : "password"}
              value={digit}
              onChange={(e) => handlePinChange(index, e.target.value, true)}
              className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
              maxLength={1}
            />
          ))}
        </div>

        <button
          onClick={() => setShowPin(!showPin)}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mx-auto mb-8"
        >
          {showPin ? <EyeSlash size="16" /> : <Eye size="16" />}
          <span className="text-sm">{showPin ? "Hide" : "Show"} PIN</span>
        </button>
      </div>

      <button
        onClick={() => setCurrentStep(2)}
        disabled={!isPinComplete}
        className={`w-full py-3 px-4 rounded-full font-medium ${
          isPinComplete
            ? "bg-primary text-white hover:bg-primary-600"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
      >
        Set PIN
      </button>
    </div>
  );

  const renderStep2 = () => (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-8">
        Move your funds safely to your bank account
      </p>

      <div className="mb-6">
        <p className="text-gray-700 font-medium mb-4">
          Select the account you would want your funds sent into
        </p>

        <div className="space-y-3 mb-6">
          {/* Existing Account */}
          <div
            onClick={() => setSelectedAccount("existing")}
            className={`p-4 border-2 rounded-2xl cursor-pointer transition-all ${
              selectedAccount === "existing"
                ? "border-primary bg-primary-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium">
                  T
                </div>
                <div>
                  <p className="font-medium text-gray-900">Ade Boluwatife</p>
                  <p className="text-sm text-gray-600">GT Bank</p>
                  <p className="text-sm text-gray-500">**********</p>
                </div>
              </div>
              {selectedAccount === "existing" && (
                <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              )}
            </div>
          </div>

          {/* Add New Account */}
          <div
            onClick={() => setSelectedAccount("new")}
            className={`p-4 border-2 border-dashed rounded-2xl cursor-pointer transition-all ${
              selectedAccount === "new"
                ? "border-primary bg-primary-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xs">
                  +
                </div>
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  Add New Withdrawal Account
                </p>
                <button className="text-sm text-primary bg-primary-100 px-3 py-1 rounded-full mt-1">
                  Add a card
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={() => setCurrentStep(3)}
        disabled={!selectedAccount}
        className={`w-full py-3 px-4 rounded-full font-medium ${
          selectedAccount
            ? "bg-primary text-white hover:bg-primary-600"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
      >
        Continue
      </button>
    </div>
  );

  const renderStep3 = () => (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-6">Add bank details</p>

      {/* Alert */}
      <div className="bg-orange-50 border border-orange-200 rounded-2xl p-4 mb-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">E</span>
          </div>
          <p className="text-orange-800 text-sm">
            Add a new bank account to withdraw your funds. Please ensure the
            account name matches the name on your profile.
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">Bank</label>
          <select
            value={bankDetails.bank}
            onChange={(e) => {
              setBankDetails((prev) => ({ ...prev, bank: e.target.value }));
              if (e.target.value && bankDetails.accountNumber) {
                setTimeout(() => setIsVerified(true), 1000);
              }
            }}
            className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
          >
            <option value="">Select Bank</option>
            <option value="GTBank">GT Bank</option>
            <option value="Access Bank">Access Bank</option>
            <option value="First Bank">First Bank</option>
          </select>
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            Account Number
          </label>
          <input
            type="text"
            value={bankDetails.accountNumber}
            onChange={(e) => {
              setBankDetails((prev) => ({
                ...prev,
                accountNumber: e.target.value,
              }));
              if (e.target.value && bankDetails.bank) {
                setTimeout(() => setIsVerified(true), 1000);
              }
            }}
            placeholder="**********"
            className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
          />
          {isVerified && (
            <div className="flex items-center gap-2 mt-2">
              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm font-medium">
                ADE BOLUWATIFE
              </span>
            </div>
          )}
        </div>
      </div>

      <button
        onClick={() => setCurrentStep(4)}
        disabled={!isVerified}
        className={`w-full py-3 px-4 rounded-full font-medium ${
          isVerified
            ? "bg-primary text-white hover:bg-primary-600"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
      >
        Continue
      </button>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-8">
      {/* Success Icon */}
      <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
        <div className="text-2xl md:text-4xl">🎉</div>
      </div>

      {/* Content */}
      <h2 className="md:text-2xl text-xl font-semibold text-gray-900 mb-2 md:whitespace-nowrap">
        Your Transaction pin has
      </h2>
      <p className="text-lg text-gray-600 mb-6">been set successfully</p>

      <p className="text-gray-600 mb-8">
        You're all set. You can now safely withdraw your funds into your
        account.
      </p>

      {/* Continue Button */}
      <button
        onClick={onClose}
        className="bg-primary text-white py-3 px-8 rounded-full font-medium hover:bg-primary-600 transition-colors"
      >
        Continue
      </button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl w-full max-w-[522px] h max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft size="20" color="#666" />
            </button>
            <span className="text-gray-600">Withdraw from Wallet</span>
            <div className="w-8"></div>
          </div>

          {currentStep < 4 && renderStepIndicator()}

          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderSuccess()}
        </div>
      </div>
    </div>
  );
};
