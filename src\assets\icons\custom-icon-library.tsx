import { IconPropTypes } from '../../components/icons/icon';

const logo = (props: IconPropTypes) => (
  <svg
    width="36"
    height="36"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M13.2179 35.0583C12.2588 33.9665 12.8123 31.1727 13.1648 29.516C13.4454 28.2385 13.7145 26.9193 14.1088 25.7024C15.2384 21.9002 18.8701 19.9365 21.9407 23.0829C23.0742 24.2884 23.7148 25.9261 24.3517 27.4803C25.0758 29.1938 26.1978 32.0787 25.8529 33.5344C25.6027 34.6602 24.708 34.2281 23.9119 33.5192C22.9491 32.6511 21.9786 31.6048 21.0536 30.7291C20.3713 30.0657 19.4008 29.3038 18.4379 29.8421C17.8428 30.1681 17.4485 30.7178 17.0088 31.3015C16.6524 31.7868 16.2733 32.3251 15.9018 32.8368C15.3218 33.5723 14.0898 35.7255 13.2179 35.0583Z"
      fill="#610037"
    />
    <path
      d="M0.0331115 18.4804C0.776122 17.2332 3.6041 16.8958 5.28725 16.7176C6.58752 16.5925 7.9295 16.4371 9.20702 16.4371C13.1723 16.3348 16.1633 19.1817 14.1162 23.0787C13.3239 24.5306 11.9592 25.6451 10.6817 26.7331C9.27526 27.9538 6.87942 29.9099 5.38961 30.0312C4.24098 30.1411 4.37746 29.1555 4.80583 28.1774C5.33276 26.9947 6.03027 25.7437 6.57616 24.5989C6.99315 23.7459 7.42151 22.5859 6.61026 21.8391C6.11366 21.3728 5.46921 21.1681 4.78306 20.9331C4.21064 20.7435 3.58137 20.5502 2.97862 20.3569C2.09914 20.0309 -0.327017 19.5229 0.0369061 18.4918L0.0331115 18.4804Z"
      fill="#610037"
    />
    <path
      d="M11.7247 0.818938C13.1425 1.14116 14.3366 3.72654 15.0266 5.27321C15.5497 6.47112 16.1107 7.69937 16.505 8.91245C17.828 12.654 16.0425 16.3767 11.7057 15.6337C10.0795 15.3266 8.60103 14.3751 7.16808 13.4956C5.57213 12.5365 2.97537 10.861 2.39537 9.48108C1.93667 8.42342 2.91473 8.24904 3.97618 8.35139C5.26507 8.48786 6.66769 8.7646 7.92626 8.92761C8.86639 9.06029 10.1022 9.10957 10.5647 8.10499C10.8528 7.49087 10.849 6.81609 10.8642 6.08824C10.8642 5.4855 10.8604 4.82588 10.8566 4.19281C10.8945 3.25646 10.6254 0.792402 11.7209 0.818938H11.7285H11.7247Z"
      fill="#610037"
    />
    <path
      d="M32.1304 6.48247C32.2631 7.93059 30.1743 9.86393 28.9157 10.9974C27.9377 11.8655 26.9445 12.7753 25.9096 13.5297C22.7594 15.9445 18.669 15.3986 18.0321 11.0429C17.8199 9.40144 18.271 7.69934 18.6652 6.06548C19.086 4.24965 19.8745 1.26245 21.008 0.288195C21.8723 -0.473769 22.3424 0.401919 22.5698 1.44062C22.839 2.70677 23.0096 4.12835 23.2408 5.37554C23.4038 6.31189 23.7412 7.49843 24.8368 7.63111C25.5115 7.71451 26.1522 7.50222 26.8497 7.28993C27.4221 7.10797 28.0476 6.89568 28.6466 6.69855C29.5488 6.44456 31.812 5.42861 32.1228 6.47869V6.48626L32.1304 6.48247Z"
      fill="#610037"
    />
    <path
      d="M33.0558 27.6396C31.7214 28.212 29.2346 26.8246 27.7675 25.9754C26.6416 25.312 25.4665 24.6524 24.4316 23.898C21.1638 21.6462 20.417 17.59 24.3633 15.6377C25.8607 14.9288 27.6159 14.8302 29.2915 14.7014C31.1452 14.5421 34.2348 14.3678 35.5123 15.1449C36.5055 15.7325 35.8193 16.4489 34.8981 16.9872C33.776 17.6355 32.4796 18.2344 31.3651 18.841C30.5235 19.2845 29.4999 19.9707 29.7122 21.0549C29.8411 21.722 30.2392 22.2641 30.6562 22.8631C31.0087 23.3521 31.403 23.879 31.7745 24.3908C32.2938 25.1717 33.958 27.0065 33.0596 27.6282H33.0558V27.6396Z"
      fill="#610037"
    />
  </svg>
);

const google = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_6938_21303)">
      <path
        d="M23.7663 12.2765C23.7663 11.4608 23.7001 10.6406 23.559 9.83813H12.2402V14.4591H18.722C18.453 15.9495 17.5888 17.2679 16.3233 18.1056V21.104H20.1903C22.4611 19.014 23.7663 15.9274 23.7663 12.2765Z"
        fill="#4285F4"
      />
      <path
        d="M12.2401 24.0008C15.4766 24.0008 18.2059 22.9382 20.1945 21.1039L16.3276 18.1055C15.2517 18.8375 13.8627 19.252 12.2445 19.252C9.11388 19.252 6.45946 17.1399 5.50705 14.3003H1.5166V17.3912C3.55371 21.4434 7.7029 24.0008 12.2401 24.0008Z"
        fill="#34A853"
      />
      <path
        d="M5.50277 14.3002C5.00011 12.8099 5.00011 11.196 5.50277 9.70569V6.61475H1.51674C-0.185266 10.0055 -0.185266 14.0004 1.51674 17.3912L5.50277 14.3002Z"
        fill="#FBBC04"
      />
      <path
        d="M12.2401 4.74966C13.9509 4.7232 15.6044 5.36697 16.8434 6.54867L20.2695 3.12262C18.1001 1.0855 15.2208 -0.034466 12.2401 0.000808666C7.7029 0.000808666 3.55371 2.55822 1.5166 6.61481L5.50264 9.70575C6.45064 6.86173 9.10947 4.74966 12.2401 4.74966Z"
        fill="#EA4335"
      />
    </g>
    <defs>
      <clipPath id="clip0_6938_21303">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const facebook = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_6938_18299)">
      <path
        d="M24.5 12C24.5 5.37258 19.1274 0 12.5 0C5.87258 0 0.5 5.37258 0.5 12C0.5 17.9895 4.8882 22.954 10.625 23.8542V15.4688H7.57812V12H10.625V9.35625C10.625 6.34875 12.4166 4.6875 15.1576 4.6875C16.4701 4.6875 17.8438 4.92188 17.8438 4.92188V7.875H16.3306C14.84 7.875 14.375 8.80008 14.375 9.75V12H17.7031L17.1711 15.4688H14.375V23.8542C20.1118 22.954 24.5 17.9895 24.5 12Z"
        fill="#1877F2"
      />
      <path
        d="M17.1711 15.4688L17.7031 12H14.375V9.75C14.375 8.80102 14.84 7.875 16.3306 7.875H17.8438V4.92188C17.8438 4.92188 16.4705 4.6875 15.1576 4.6875C12.4166 4.6875 10.625 6.34875 10.625 9.35625V12H7.57812V15.4688H10.625V23.8542C11.8674 24.0486 13.1326 24.0486 14.375 23.8542V15.4688H17.1711Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_6938_18299">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);
const loading = () => (
  <svg
    className="animate-spin h-5 w-5 text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24">
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);
const marked = () => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <rect y="1.5" width="11" height="11" rx="5.5" fill="#7177F5" />
    <rect
      y="1.5"
      width="11"
      height="11"
      rx="5.5"
      stroke="#7177F5"
      stroke-width="2"
    />
    <path
      d="M7.5 5.5L4.75 8.25L3.5 7"
      stroke="white"
      stroke-width="0.6"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
const confirmed = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z"
      fill="#EBF9EF"
    />
    <path
      d="M21 14C21 10.134 17.866 7 14 7C10.134 7 7 10.134 7 14C7 17.866 10.134 21 14 21C17.866 21 21 17.866 21 14Z"
      fill="#3CC35C"
    />
    <path
      d="M21 14C21 10.134 17.866 7 14 7C10.134 7 7 10.134 7 14C7 17.866 10.134 21 14 21C17.866 21 21 17.866 21 14Z"
      stroke="#3CC35C"
      stroke-width="2"
    />
    <mask
      id="mask0_57_28"
      mask-type="luminance"
      maskUnits="userSpaceOnUse"
      x="10"
      y="10"
      width="8"
      height="8">
      <path
        d="M17.8185 10.1816H10.1821V17.8179H17.8185V10.1816Z"
        fill="white"
      />
    </mask>
    <g mask="url(#mask0_57_28)">
      <path
        d="M16.5461 12.0908L13.0461 15.5908L11.4551 13.9999"
        stroke="white"
        stroke-width="1.4"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
  </svg>
);
const profile = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z"
      fill="#F5F6FE"
    />
    <path
      d="M12.249 8.1665C10.721 8.1665 9.479 9.409 9.479 10.9373C9.479 12.4365 10.651 13.6498 12.179 13.7023C12.226 13.6965 12.273 13.6965 12.308 13.7023C12.319 13.7023 12.325 13.7023 12.337 13.7023C12.343 13.7023 12.343 13.7023 12.349 13.7023C13.842 13.6498 15.014 12.4365 15.02 10.9373C15.02 9.409 13.778 8.1665 12.249 8.1665Z"
      fill="#5F66F3"
    />
    <path
      d="M15.2131 15.2542C13.5851 14.1692 10.9311 14.1692 9.29207 15.2542C8.55107 15.75 8.14307 16.4208 8.14307 17.1383C8.14307 17.8558 8.55107 18.5208 9.28607 19.0108C10.1031 19.5592 11.1761 19.8333 12.2491 19.8333C13.3231 19.8333 14.3961 19.5592 15.2131 19.0108C15.9481 18.515 16.3561 17.85 16.3561 17.1267C16.3501 16.4092 15.9481 15.7442 15.2131 15.2542Z"
      fill="#5F66F3"
    />
    <path
      d="M18.66 11.2816C18.754 12.4132 17.949 13.4049 16.835 13.5391C16.829 13.5391 16.829 13.5391 16.823 13.5391H16.805C16.77 13.5391 16.735 13.5391 16.706 13.5507C16.14 13.5799 15.621 13.3991 15.23 13.0666C15.831 12.5299 16.175 11.7249 16.105 10.8499C16.065 10.3774 15.901 9.94573 15.656 9.57823C15.878 9.46743 16.135 9.39743 16.397 9.37413C17.54 9.27493 18.561 10.1266 18.66 11.2816Z"
      fill="#5F66F3"
    />
    <path
      d="M19.8272 16.6776C19.7802 17.2434 19.4181 17.7334 18.8121 18.0659C18.2281 18.3867 17.4932 18.5384 16.7642 18.5209C17.1842 18.1417 17.4292 17.6692 17.4762 17.1676C17.5342 16.4442 17.1902 15.7501 16.5022 15.1959C16.1112 14.8867 15.6562 14.6417 15.1602 14.4609C16.4492 14.0876 18.0712 14.3384 19.0682 15.1434C19.6052 15.5751 19.8792 16.1176 19.8272 16.6776Z"
      fill="#5F66F3"
    />
  </svg>
);
const pending = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z"
      fill="#FFF3E6"
    />
    <path
      d="M13.999 8.1665C10.785 8.1665 8.16602 10.7857 8.16602 13.9998C8.16602 17.214 10.785 19.8332 13.999 19.8332C17.214 19.8332 19.833 17.214 19.833 13.9998C19.833 10.7857 17.214 8.1665 13.999 8.1665ZM13.562 11.6665C13.562 11.4273 13.76 11.229 13.999 11.229C14.239 11.229 14.437 11.4273 14.437 11.6665V14.5832C14.437 14.8223 14.239 15.0207 13.999 15.0207C13.76 15.0207 13.562 14.8223 13.562 14.5832V11.6665ZM14.536 16.5548C14.507 16.6307 14.466 16.689 14.414 16.7473C14.355 16.7998 14.291 16.8407 14.221 16.8698C14.151 16.899 14.075 16.9165 13.999 16.9165C13.924 16.9165 13.848 16.899 13.778 16.8698C13.708 16.8407 13.644 16.7998 13.585 16.7473C13.533 16.689 13.492 16.6307 13.463 16.5548C13.434 16.4848 13.416 16.409 13.416 16.3332C13.416 16.2573 13.434 16.1815 13.463 16.1115C13.492 16.0415 13.533 15.9773 13.585 15.919C13.644 15.8665 13.708 15.8257 13.778 15.7965C13.918 15.7382 14.081 15.7382 14.221 15.7965C14.291 15.8257 14.355 15.8665 14.414 15.919C14.466 15.9773 14.507 16.0415 14.536 16.1115C14.565 16.1815 14.583 16.2573 14.583 16.3332C14.583 16.409 14.565 16.4848 14.536 16.5548Z"
      fill="#FF8C04"
    />
  </svg>
);
const declined = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z"
      fill="#FFE5E5"
    />
    <path
      d="M13.999 8.1665C10.785 8.1665 8.16602 10.7857 8.16602 13.9998C8.16602 17.214 10.785 19.8332 13.999 19.8332C17.214 19.8332 19.833 17.214 19.833 13.9998C19.833 10.7857 17.214 8.1665 13.999 8.1665ZM15.959 15.3415C16.129 15.5107 16.129 15.7907 15.959 15.9598C15.872 16.0473 15.761 16.0882 15.65 16.0882C15.539 16.0882 15.429 16.0473 15.341 15.9598L13.999 14.6182L12.658 15.9598C12.57 16.0473 12.459 16.0882 12.349 16.0882C12.238 16.0882 12.127 16.0473 12.039 15.9598C11.87 15.7907 11.87 15.5107 12.039 15.3415L13.381 13.9998L12.039 12.6582C11.87 12.489 11.87 12.209 12.039 12.0398C12.209 11.8707 12.489 11.8707 12.658 12.0398L13.999 13.3815L15.341 12.0398C15.51 11.8707 15.79 11.8707 15.959 12.0398C16.129 12.209 16.129 12.489 15.959 12.6582L14.618 13.9998L15.959 15.3415Z"
      fill="#FF0000"
    />
  </svg>
);
const twitter = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_6308_105578)">
      <path
        d="M9.39829 6.87749L14.8589 0.666504H13.5651L8.82149 6.05829L5.03558 0.666504H0.667969L6.39429 8.82057L0.667969 15.3332H1.96178L6.96803 9.63793L10.967 15.3332H15.3346L9.39829 6.87749ZM8.14244 6.83528L8.72107 7.64775L13.5644 14.4253L8.14244 6.83528Z"
        fill="#A6AAF9"
      />
    </g>
    <defs>
      <clipPath id="clip0_6308_105578">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
const cashGift = ({
  color = '#F5F4FF',
  secondaryColor = '#7F7AB2',
}: { color?: string; secondaryColor?: string } = {}) => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <circle cx="14" cy="14" r="14" fill={color} />
    <path
      opacity="0.4"
      d="M19.8307 12.2503V15.7503H19.2474C17.4974 15.7503 16.9141 16.3337 16.9141 18.0837V18.9587H11.0807V18.0837C11.0807 16.3337 10.4974 15.7503 8.7474 15.7503H8.16406V12.2503H8.7474C10.4974 12.2503 11.0807 11.667 11.0807 9.91699V9.04199H16.9141V9.91699C16.9141 11.667 17.4974 12.2503 19.2474 12.2503H19.8307Z"
      fill={secondaryColor}
    />
    <path
      d="M14 15.75C14.9665 15.75 15.75 14.9665 15.75 14C15.75 13.0335 14.9665 12.25 14 12.25C13.0335 12.25 12.25 13.0335 12.25 14C12.25 14.9665 13.0335 15.75 14 15.75Z"
      fill={secondaryColor}
    />
    <path
      d="M11.0807 9.04199V9.91699C11.0807 11.667 10.4974 12.2503 8.7474 12.2503H8.16406V11.9587C8.16406 9.91699 9.33073 9.04199 11.0807 9.04199Z"
      fill={secondaryColor}
    />
    <path
      d="M19.8307 11.9587V12.2503H19.2474C17.4974 12.2503 16.9141 11.667 16.9141 9.91699V9.04199C18.6641 9.04199 19.8307 9.91699 19.8307 11.9587Z"
      fill={secondaryColor}
    />
    <path
      d="M11.0807 18.0833V18.9583C9.33073 18.9583 8.16406 18.0833 8.16406 16.0417V15.75H8.7474C10.4974 15.75 11.0807 16.3333 11.0807 18.0833Z"
      fill={secondaryColor}
    />
    <path
      d="M19.8307 15.75V16.0417C19.8307 18.0833 18.6641 18.9583 16.9141 18.9583V18.0833C16.9141 16.3333 17.4974 15.75 19.2474 15.75H19.8307Z"
      fill={secondaryColor}
    />
  </svg>
);
const giftItems = ({
  color = '#F5F6FE',
  secondaryColor = '#5F66F3',
}: { color?: string; secondaryColor?: string } = {}) => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <circle cx="14" cy="14" r="14" fill={color} />
    <path
      opacity="0.4"
      d="M18.6458 12.833V17.4997C18.6458 19.2497 18.0625 19.833 16.3125 19.833H11.6458C9.89583 19.833 9.3125 19.2497 9.3125 17.4997V12.833H18.6458Z"
      fill={secondaryColor}
    />
    <path
      d="M19.5443 11.0837V11.667C19.5443 12.3087 19.2351 12.8337 18.3776 12.8337H9.6276C8.7351 12.8337 8.46094 12.3087 8.46094 11.667V11.0837C8.46094 10.442 8.7351 9.91699 9.6276 9.91699H18.3776C19.2351 9.91699 19.5443 10.442 19.5443 11.0837Z"
      fill={secondaryColor}
    />
    <path
      opacity="0.4"
      d="M13.7864 9.91651H10.5664C10.368 9.70068 10.3739 9.36818 10.5839 9.15818L11.4122 8.32984C11.628 8.11401 11.9839 8.11401 12.1997 8.32984L13.7864 9.91651Z"
      fill={secondaryColor}
    />
    <path
      opacity="0.4"
      d="M17.4231 9.91651H14.2031L15.7898 8.32984C16.0056 8.11401 16.3615 8.11401 16.5773 8.32984L17.4056 9.15818C17.6156 9.36818 17.6215 9.70068 17.4231 9.91651Z"
      fill={secondaryColor}
    />
    <path
      opacity="0.6"
      d="M12.2188 12.833V15.8313C12.2188 16.298 12.7321 16.5722 13.1229 16.3213L13.6713 15.9597C13.8696 15.8313 14.1204 15.8313 14.3129 15.9597L14.8321 16.3097C15.2171 16.5663 15.7363 16.2922 15.7363 15.8255V12.833H12.2188Z"
      fill={secondaryColor}
    />
  </svg>
);
const helpCircle = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_9805_7196)">
      <path
        d="M4.545 4.5C4.66255 4.16583 4.89458 3.88405 5.19998 3.70457C5.50538 3.52508 5.86445 3.45947 6.21359 3.51936C6.56273 3.57924 6.87941 3.76076 7.10754 4.03176C7.33567 4.30277 7.46053 4.64576 7.46 5C7.46 6 5.96 6.5 5.96 6.5M6 8.5H6.005M11 6C11 8.76142 8.76142 11 6 11C3.23858 11 1 8.76142 1 6C1 3.23858 3.23858 1 6 1C8.76142 1 11 3.23858 11 6Z"
        stroke="#A6AAF9"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_9805_7196">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
const reserved = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <circle cx="14" cy="14" r="14" fill="#FFF3E6" />
    <path
      d="M19.8151 17.5013V18.0846C19.8151 19.0471 19.8151 19.8346 18.0651 19.8346H9.89844C8.14844 19.8346 8.14844 19.0471 8.14844 18.0846V17.5013C8.14844 17.1805 8.41094 16.918 8.73177 16.918H19.2318C19.5526 16.918 19.8151 17.1805 19.8151 17.5013Z"
      fill="#FF8C04"
    />
    <path
      opacity="0.4"
      d="M19.0854 14.5846V16.918H8.90625V14.5846C8.90625 12.3446 10.4871 10.4721 12.5929 10.023C12.9079 9.95297 13.2346 9.91797 13.5729 9.91797H14.4188C14.7571 9.91797 15.0896 9.95297 15.4046 10.023C17.5104 10.478 19.0854 12.3446 19.0854 14.5846Z"
      fill="#FF8C04"
    />
    <path
      d="M15.4557 9.6263C15.4557 9.7663 15.4382 9.89464 15.4032 10.023C15.0882 9.95297 14.7557 9.91797 14.4174 9.91797H13.5716C13.2332 9.91797 12.9066 9.95297 12.5916 10.023C12.5566 9.89464 12.5391 9.7663 12.5391 9.6263C12.5391 8.8213 13.1924 8.16797 13.9974 8.16797C14.8024 8.16797 15.4557 8.8213 15.4557 9.6263Z"
      fill="#FF8C04"
    />
    <path
      d="M15.75 13.8555H12.25C12.0108 13.8555 11.8125 13.6571 11.8125 13.418C11.8125 13.1788 12.0108 12.9805 12.25 12.9805H15.75C15.9892 12.9805 16.1875 13.1788 16.1875 13.418C16.1875 13.6571 15.9892 13.8555 15.75 13.8555Z"
      fill="#FF8C04"
    />
  </svg>
);
const bus = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <rect width="28" height="28" rx="14" fill="#EDEEFE" />
    <path
      opacity="0.4"
      d="M15.1718 8.66734C15.2164 8.66735 15.2546 8.70581 15.2548 8.75036L15.2544 14.0004C15.2544 14.3658 14.9537 14.6671 14.5883 14.6673L8.67132 14.6668L8.67151 12.4588C9.06566 12.6546 9.51247 12.7598 9.98301 12.7489L9.98398 12.7499L9.98398 12.7489L9.98594 12.7499L9.98496 12.7489C10.6993 12.7346 11.3498 12.4585 11.8366 11.9932L11.8356 11.9923C12.0547 11.8028 12.2335 11.5738 12.3698 11.3312L12.3708 11.3321C12.5947 10.9528 12.7315 10.5131 12.7528 10.0509L12.7547 9.85171C12.7438 9.42501 12.6392 9.02453 12.4628 8.66711L15.1718 8.66734Z"
      fill="#4D55F2"
      stroke="#4D55F2"
    />
    <path
      d="M19.8381 15.1677L19.8379 16.9177C19.8379 17.8861 19.0561 18.6677 18.0878 18.6676L17.5045 18.6675C17.5045 18.0259 16.9796 17.5008 16.3379 17.5008C15.6962 17.5007 15.1712 18.0257 15.1711 18.6673L12.8378 18.6671C12.8379 18.0255 12.3129 17.5004 11.6712 17.5004C11.0296 17.5003 10.5045 18.0253 10.5045 18.6669L9.92113 18.6669C8.9528 18.6668 8.1712 17.8851 8.17128 16.9167L8.17143 15.1667L14.5881 15.1673C15.2298 15.1673 15.7548 14.6424 15.7549 14.0007L15.7552 9.91739L16.8286 9.91749C17.2486 9.91752 17.6335 10.1451 17.8435 10.5067L18.8408 12.251L18.0883 12.2509C17.7675 12.2509 17.505 12.5134 17.505 12.8342L17.5048 14.5842C17.5048 14.905 17.7673 15.1676 18.0881 15.1676L19.8381 15.1677Z"
      fill="#4D55F2"
    />
    <path
      opacity="0.4"
      d="M11.6743 19.8337C12.3186 19.8338 12.841 19.3115 12.841 18.6672C12.8411 18.0228 12.3188 17.5005 11.6745 17.5004C11.0301 17.5003 10.5078 18.0226 10.5077 18.667C10.5077 19.3113 11.0299 19.8337 11.6743 19.8337Z"
      fill="#4D55F2"
    />
    <path
      opacity="0.4"
      d="M16.3383 19.8341C16.9827 19.8342 17.5051 19.3119 17.5051 18.6675C17.5052 18.0232 16.9829 17.5008 16.3385 17.5008C15.6942 17.5007 15.1718 18.023 15.1718 18.6673C15.1717 19.3117 15.694 19.834 16.3383 19.8341Z"
      fill="#4D55F2"
    />
    <path
      opacity="0.4"
      d="M19.841 14.3103L19.8409 15.1678L18.0909 15.1676C17.7701 15.1676 17.5076 14.9051 17.5076 14.5842L17.5078 12.8342C17.5078 12.5134 17.7703 12.2509 18.0911 12.251L18.8436 12.251L19.6893 13.7328C19.7885 13.9078 19.841 14.1061 19.841 14.3103Z"
      fill="#4D55F2"
    />
    <path
      d="M11.4677 8.16705C11.0477 7.79368 10.4877 7.57197 9.87524 7.58358C9.3269 7.5952 8.82523 7.79932 8.42854 8.12595C7.9035 8.56341 7.57678 9.23423 7.59422 9.96923C7.60585 10.4067 7.73415 10.8151 7.96162 11.1593C8.02578 11.2584 8.09576 11.3576 8.17742 11.4451C8.60321 11.9526 9.25654 12.2677 9.97987 12.2503C10.569 12.2386 11.0999 12.0112 11.4966 11.6321C11.6774 11.4804 11.8291 11.2879 11.9458 11.0779C12.1558 10.7221 12.2725 10.3021 12.2609 9.8646C12.2435 9.1821 11.9402 8.58126 11.4677 8.16705ZM11.1292 9.63703L9.90996 10.8152C9.82245 10.8969 9.71747 10.9377 9.60664 10.9377C9.49581 10.9377 9.3908 10.8969 9.30331 10.8152L8.71418 10.2552C8.5392 10.086 8.53341 9.81181 8.70259 9.63682C8.87177 9.46184 9.14594 9.45602 9.32093 9.6252L9.60673 9.89937L10.5226 9.01281C10.6977 8.84366 10.9718 8.84949 11.141 9.0245C11.3101 9.19369 11.3043 9.46788 11.1292 9.63703Z"
      fill="#4D55F2"
    />
  </svg>
);
const markedCircle = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <circle cx="14" cy="14" r="14" fill="#EBF9EF" />
    <path
      d="M13.9974 8.16797C10.7832 8.16797 8.16406 10.7871 8.16406 14.0013C8.16406 17.2155 10.7832 19.8346 13.9974 19.8346C17.2116 19.8346 19.8307 17.2155 19.8307 14.0013C19.8307 10.7871 17.2116 8.16797 13.9974 8.16797ZM16.7857 12.6596L13.4782 15.9671C13.3966 16.0488 13.2857 16.0955 13.1691 16.0955C13.0524 16.0955 12.9416 16.0488 12.8599 15.9671L11.2091 14.3163C11.0399 14.1471 11.0399 13.8671 11.2091 13.698C11.3782 13.5288 11.6582 13.5288 11.8274 13.698L13.1691 15.0396L16.1674 12.0413C16.3366 11.8721 16.6166 11.8721 16.7857 12.0413C16.9549 12.2105 16.9549 12.4846 16.7857 12.6596Z"
      fill="#3CC35C"
    />
  </svg>
);
const IconLib = {
  logo,
  google,
  facebook,
  loading,
  marked,
  profile,
  confirmed,
  pending,
  declined,
  helpCircle,
  twitter,
  bus,
  giftItems,
  cashGift,
  markedCircle,
  reserved,
};

export type IconType = keyof typeof IconLib;
export default IconLib;
