@import "tailwindcss";

/* EventPark Custom CSS Variables */
:root {
  --EVENTPARK-SHADOWS-SM: 0px 12px 120px 0px rgba(95, 95, 95, 0.06);
  --gray-100: rgba(245, 245, 245, 1);
  --gray-500: rgba(113, 118, 128, 1);
  --gray-700: rgba(65, 70, 81, 1);
  --shadow-xs: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);
  --white: rgba(255, 255, 255, 1);

  /* Shadcn/ui variables */
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: transparent;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --ring: 215 20.2% 65.1%;
  --radius: 0.5rem;
}

.dark {
  --background: 224 71% 4%;
  --foreground: 213 31% 91%;
  --muted: 223 47% 11%;
  --muted-foreground: 215.4 16.3% 56.9%;
  --accent: 216 34% 17%;
  --accent-foreground: 210 40% 98%;
  --popover: 224 71% 4%;
  --popover-foreground: 215 20.2% 65.1%;
  --border: 216 34% 17%;
  --input: 216 34% 17%;
  --card: transparent;
  --card-foreground: 213 31% 91%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 1.2%;
  --secondary: 222.2 47.4% 11.2%;
  --secondary-foreground: 210 40% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --ring: 216 34% 17%;
}

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

@layer base {
  * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    @apply font-rethink border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

button {
  @apply cursor-pointer;
}

@theme {
  --color-primary: #4d55f2;
  --color-primary-50: #1a22bf;
  --color-primary-100: #f7f3e8;
  --color-primary-110: #b8bbfa;
  --color-primary-120: #8288f6;
  --color-primary-130: #f5f4ff;
  --color-primary-140: #b1ace5;
  --color-primary-150: #f5f6fe;
  --color-primary-200: #9cc1fc;
  --color-primary-250: #edeefe;
  --color-primary-300: #a6aaf9;
  --color-primary-350: #f3f5f8;
  --color-primary-400: #caccfb;
  --color-primary-450: #edf0f4;
  --color-primary-500: #0109a5;
  --color-primary-550: #032863;
  --color-primary-600: #cde0fd;
  --color-primary-650: #343cd8;
  --color-primary-700: #ebf3fe;
  --color-primary-750: #5f66f3;
  --color-primary-800: #000f4a;
  --color-primary-850: #82a7e2;
  --color-primary-900: #e1ecfe;
  --color-primary-950: #dbddfc;
  --color-primary-960: #125CF2;
  /* orange */
  --color-cus-orange: #ff5519;
  --color-cus-orange-100: #ff6630;
  --color-cus-orange-150: #ffccba;
  --color-cus-orange-200: #330000;
  --color-cus-orange-250: #ff885e;
  --color-cus-orange-300: #ff9975;
  --color-cus-orange-400: #4d0000;
  --color-cus-orange-500: #cc2200;
  --color-cus-orange-600: #b54708;
  --color-cus-orange-700: #ff7747;
  --color-cus-orange-800: #ff9500;

  /* black */
  --color-dark: #241c05;
  --color-dark-100: #00000d;
  --color-dark-200: #000026;
  --color-dark-300: #1a0000;
  /* green */
  --color-grin: #ecfdf3;
  --color-grin-50: #ebf9ef;
  --color-grin-100: #027a48;

  /* dark-blue */
  --color-dark-blue: #000073;
  --color-dark-blue-100: #00008c;
  --color-dark-blue-200: #000059;
  --color-dark-blue-300: #1d427d;
  --color-dark-blue-400: #000040;
  --color-dark-blue-500: #000030;
  --color-dark-blue-600: #322d66;

  /* light-blue */
  --color-light-blue: #f6f7f9;
  --color-light-blue-50: #fbf9fa;
  --color-light-blue-100: #faf9ff;
  --color-light-blue-150: #f4f3ff;

  /* red */
  --color-cus-red: #990000;
  --color-cus-red-100: #b42318;

  /* purple */
  --color-perple: #5925dc;
  --color-perple-50: #6941c6;

  /*pink*/
  --color-cus-pink: #fefaf8;
  --color-cus-pink-50: #fff4f0;
  --color-cus-pink-100: #967f75;
  --color-cus-pink-150: #fffaeb;
  --color-cus-pink-200: #e2cbc1;
  --color-cus-pink-250: #fef3f2;
  --color-cus-pink-300: #fdf2ed;
  --color-cus-pink-400: #fdeae2;
  --color-cus-pink-500: #fdede6;
  --color-cus-pink-600: #fdefe9;
  --color-cus-pink-700: #fff5f5;
  --color-cus-pink-900: #ffeee8;

  /* Grey */

  --color-grey: #4d4d4d;
  --color-grey-50: #181d27;
  --color-grey-100: #8e8e93;
  --color-grey-110: #eae8ff;
  --color-grey-120: #333333;
  --color-grey-130: #f8f9fb;
  --color-grey-150: #f0f0f0;
  --color-grey-200: #d5d7da;
  --color-grey-250: #808080;
  --color-grey-300: #717680;
  --color-grey-350: #fafafa;
  --color-grey-400: #f2f2f7;
  --color-grey-450: #f2f2f7;
  --color-grey-500: #414651;
  --color-grey-550: #999999;
  --color-grey-600: #e3e3e3;
  --color-grey-650: #535862;
  --color-grey-700: #b3b3b3;
  --color-grey-750: #090909;
  --color-grey-800: #e5e5ea;
  --color-grey-850: #f5f5f5;
  --color-grey-900: #e6e6e6;
  --color-grey-950: #666666;

  --font-rethink: "Rethink Sans", sans-serif;
  --font-brush: "Brush Script MT", "cursive";
  --font-inter: "Inter", sans-serif;

  /* Additional Tailwind v4 theme extensions */
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary-default: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary-default: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-destructive-default: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-muted-default: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent-default: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-popover-default: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-card-default: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  /* Box shadows */
  --shadow-EVENTPARK-SHADOWS-SM: var(--EVENTPARK-SHADOWS-SM);
  --shadow-shadow-xs: var(--shadow-xs);

  /* Border radius */
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}
