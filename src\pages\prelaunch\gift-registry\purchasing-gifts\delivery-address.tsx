import { ArrowCircleLeft2, ArrowCircleRight2 } from 'iconsax-react';
import { Button } from '../../../../components/button/onboardingButton';
import truck from '../../../../assets/images/truck-delivery.png';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface DeliveryAddressProps {
  onClose: () => void;
}

export const DeliveryAddress: React.FC<DeliveryAddressProps> = ({
  onClose,
}) => {
  const navigate = useNavigate()
  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    };
  }, []);
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="pt-20">
          <Button
            variant="primary"
            size="md"
            onClick={onClose}
            className="text-primary-650 bg-white mb-5"
            iconLeft={
              <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
            }>
            Back to Gift items
          </Button>
          <div className="bg-white pl-19 pr-30 pt-6 pb-9 rounded-2xl">
            <div className=" mb-11 -ml-4">
              <img
                src={truck}
                alt="delivery-truck"
                className="h-[108px] w-[105px]"
              />
            </div>
            <h2 className="text-[22px] font-medium text-grey-750 mb-1">
              Delivery Address
            </h2>
            <p className="text-sm text-grey-100 mb-12">
              Before you leave you'll need the recipient's delivery address
            </p>
            <p className=" text-base font-medium mb-17 leading-relaxed max-w-[497px] w-full">
              We currently don't have this recipient's delivery address, please
              ask them where they would like their gifts delivered
            </p>
            <Button
              variant="primary"
              size="md"
              onClick={() => navigate('/back-from-jumia')}
              className={`text-white  mt-5  bg-primary-650 `}
              iconRight={
                <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
              }>
              Proceed{' '}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
