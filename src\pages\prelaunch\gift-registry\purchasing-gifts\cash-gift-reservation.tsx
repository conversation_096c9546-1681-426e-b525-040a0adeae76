import { Arrow<PERSON>ircleLeft2, Tag2, Tick<PERSON>ircle } from "iconsax-react";
import { But<PERSON> } from "../../../../components/button/onboardingButton";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { StepProgress } from "../../../../components/step-progress/step-progress";
import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { GifterDetails } from "./gifter-details";
import { CashGiftAmount } from "./cash-gift-amount";
import { PaymentModal } from "./payment-modal";
import { SuccessPayment } from "./success";
import { SendCashGift } from "./SendCashGift";

export const CashGiftReservation = () => {
  const { crowdfunding } = useParams();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([1]);
  const [showTooltip, setShowTooltip] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const isCrowdfundingEnabled = crowdfunding === "true";

  const steps = [
    { id: 1, name: "Gifter's details" },
    { id: 2, name: "Gift Reservation" },
  ];

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };

  if (showSuccess) {
    return <SuccessPayment />;
  }

  return (
    <div className="min-h-screen bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] pb-36">
      <div className="pt-14 mx-auto md:max-w-[750px] w-full px-4 md:px-0">
        <Button
          variant="primary"
          size="md"
          className="text-primary-650 bg-white hover:bg-gray-50"
          iconLeft={
            <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
          }
          onClick={() => navigate(`/giftItem-as-guest`)}
        >
          Back
        </Button>

        <div className="mt-5 flex flex-col md:flex-row bg-white rounded-2xl shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
          {/* Left Section */}
          <div className="w-full md:w-[282px] border-b md:border-b-0 md:border-r border-[#F0F0F0]">
            <div className="mt-4 mx-[18px] p-4 bg-[#F5F6FE] rounded-[14px]">
              <div className="relative w-[213px] h-[213px] mx-auto">
                <img
                  src={stackMoney}
                  alt="cash-gift"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>

            <h3 className="mx-[18px] mt-6 text-[22px] font-normal text-[#090909] leading-[1.302] tracking-[-0.02em]">
              Trip to Zanzibar for honeymoon
            </h3>

            <div className="mx-4 mt-4">
              <div className="inline-flex items-center gap-1 px-2.5 py-1.5 bg-[#F4F3FF] rounded-2xl">
                <Tag2 size={12} variant="Bulk" color="#5856D6" />
                <span className="text-sm font-medium text-[#5925DC] leading-[1.429] tracking-[-0.01em]">
                  ₦4,500,000
                </span>
              </div>
            </div>

            {/* Crowd Gifting Section */}
            {isCrowdfundingEnabled && (
              <div className="mx-4 mt-[68px] relative">
                <button
                  className="flex items-center gap-1.5 focus:outline-none"
                  onClick={() => setShowTooltip(!showTooltip)}
                >
                  <TickCircle size="20" variant="Bulk" color="#3CC35C" />
                  <span className="text-sm font-medium text-[#007AFF]">
                    Crowd gifting enabled
                  </span>
                </button>

                {showTooltip && (
                  <div className="absolute left-0 top-[32px] w-[254px] bg-[#9499F7] text-white p-3 rounded-lg shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)]">
                    <p className="text-xs leading-[1.667] tracking-[-0.03em]">
                      Contribute to this cause alongside others to make their
                      dream come true.
                    </p>
                  </div>
                )}

                <div className="mt-4 w-[226px] h-2 bg-[#FEF5F1] rounded-lg overflow-hidden">
                  <div className="w-[57.5%] h-full bg-[#4D55F2] rounded-lg" />
                </div>
                <p className="mt-2 text-sm text-[#666666] leading-[1.302] tracking-[-0.02em]">
                  ₦850,000 contributed by 4 people
                </p>
              </div>
            )}

            <div className="mt-[28px] px-5 py-[19px] border-t border-[#F5F5F5]">
              <p className="text-sm text-[#8E8E93] leading-[1.302] tracking-[-0.02em]">
                Total Amount
              </p>
              <p className="mt-1 text-base font-medium text-[#090909] leading-[1.302] tracking-[-0.04em]">
                ₦4,500,000.00
              </p>
            </div>
          </div>

          {/* Right Section */}
          <div className="flex-1 p-4 md:p-6 md:pl-0">
            <div className="md:p-6">
              <h2 className="text-[22px] font-medium text-[#090909] leading-[1.302] tracking-[-0.04em]">
                Send a Cash gift to Olatunde
              </h2>
              <p className="text-base text-[#8E8E93] leading-[1.302] tracking-[-0.02em] mt-1">
                Give something they'll cherish.
              </p>
            </div>

            <StepProgress
              steps={steps}
              activeStep={activeStep}
              completedSteps={completedSteps}
              onStepChange={handleStepChange}
            />

            {activeStep === 1 && (
              <GifterDetails
                onContinue={() => {
                  setCompletedSteps((prev) => [...new Set([...prev, 1])]);
                  setActiveStep(2);
                }}
              />
            )}
            {activeStep === 2 &&
              (isCrowdfundingEnabled ? (
                <CashGiftAmount
                  onContinue={() => {
                    setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                    setShowPaymentModal(true);
                  }}
                />
              ) : (
                <SendCashGift
                  onContinue={() => {
                    setCompletedSteps((prev) => [...new Set([...prev, 2])]);
                    setShowPaymentModal(true);
                  }}
                />
              ))}
          </div>
        </div>
      </div>

      {showPaymentModal && (
        <PaymentModal
          onPayNow={() => setShowSuccess(true)}
          onPayLater={() => navigate(`/giftItem-as-guest`)}
          onClose={() => setShowPaymentModal(false)}
        />
      )}
    </div>
  );
};
