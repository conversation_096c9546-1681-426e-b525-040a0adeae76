import { InfoCircle, Tag2, TickCircle } from "iconsax-react";
import giftIcon from "../../../../assets/images/gift-blur.png";
import { But<PERSON> } from "../../../../components/button/onboardingButton";
import { useState } from "react";
import iphone from "../../../../assets/images/iphone.svg";
import cash from "../../../../assets/images/cash-img.png";
import { Footer } from "../../footer";
import { useNavigate } from "react-router-dom";

export const ViewingGiftAsGuest = () => {
  const [activeTab, setActiveTab] = useState<"items" | "cash">("items");
  const navigate = useNavigate();

  const giftItems = [
    {
      id: 1,
      name: "iPhone 15 Pro",
      price: 1650000,
      image: iphone,
      reserved: false,
      purchased: false,
      mostWanted: true,
      description: "Flawless makeup for your big day.",
      type: "standard",
    },
    {
      id: 2,
      name: "AirPods Pro",
      price: 130000,
      image: iphone,
      reserved: false,
      purchased: true,
      mostWanted: false,
      description: "Flawless makeup for your big day.",
      type: "standard",
    },
    {
      id: 3,
      name: "MacBook Pro",
      price: 2500000,
      image: iphone,
      reserved: true,
      purchased: false,
      mostWanted: false,
      description: "Powerful laptop for all your needs.",
      type: "wishlist",
    },
    {
      id: 4,
      name: "MacBook Pro",
      price: 2500000,
      image: iphone,
      reserved: false,
      purchased: false,
      mostWanted: false,
      description: "Powerful laptop for all your needs.",
      type: "wishlist",
      crowdGiftingEnabled: true,
      contributedAmount: 850000,
      contributorCount: 4,
    },
  ];
  const cashItems = [
    {
      id: 1,
      amount: 2000000,
      description: "Trip to Zanzibar for honeymoon",
      received: 0,
      status: "available",
    },
    {
      id: 2,
      amount: 1650000,
      description: "Trip to Zanzibar for honeymoon",
      received: 12,
      status: "reserved",
    },
    {
      id: 3,
      amount: 1650000,
      description: "Trip to Zanzibar for honeymoon",
      received: 0,
      status: "most_wanted",
    },
  ];
  const handleMakePayment = () => {
    navigate("/purchasing-gift-items");
  };
  return (
    <div className="flex pt-16 sm:pt-20 min-h-screen flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
      <div
        className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
        style={{
          backgroundSize: "100% auto",
        }}
      />

      <div className="mx-auto w-full z-20 px-4 sm:px-0">
        <div className="text-center">
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src={giftIcon}
              alt="Gift"
              className="w-12 h-12 sm:w-16 sm:h-16"
            />
          </div>

          <div className="text-sm sm:text-base uppercase text-[#333333] tracking-widest mb-2 sm:mb-3 px-2">
            CURATED BY OLADELE JOHNSON
          </div>

          <h1 className="text-2xl sm:text-3xl md:text-[48px] font-bold text-[#000059] mb-2 sm:mb-3 px-2 leading-tight sm:leading-normal">
            Oladele's birthday gifts
          </h1>

          <p className="text-[#666666] leading-[150%] text-base sm:text-lg mb-3 px-4 sm:px-0">
            Celebrate with me! Pick a gift and{" "}
            <br className="hidden sm:block" />
            make my day special. 💙{" "}
          </p>

          <Button
            variant="primary"
            size="sm"
            iconLeft={<InfoCircle size="14" color="#FF6630" variant="Bulk" />}
            className="bg-[#FDEFE9] text-cus-orange mx-auto underline italic font-bold text-sm sm:text-base"
          >
            How to Get Gift
          </Button>

          <div className="relative flex justify-center mt-10 sm:mt-14">
            <div className="flex w-fit">
              <button
                className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                  activeTab === "items"
                    ? "bg-[#4D55F2] text-white"
                    : "text-gray-500"
                }`}
                onClick={() => setActiveTab("items")}
              >
                Gift Items
              </button>
              <button
                className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                  activeTab === "cash"
                    ? "bg-[#4D55F2] text-white"
                    : "text-gray-500"
                }`}
                onClick={() => setActiveTab("cash")}
              >
                Cashgifts
              </button>
            </div>

            <div
              className={`absolute bottom-[-14px] w-1.5 h-1.5 bg-[#FF6630] rounded-full transition-all duration-300 ${
                activeTab === "items"
                  ? "left-[calc(50%-50px)]"
                  : "left-[calc(50%+50px)]"
              }`}
            />
          </div>

          <div className="mt-8 sm:mt-10 mb-16 sm:mb-20 max-w-[560px] w-full mx-auto">
            {activeTab === "items" ? (
              <div>
                <div className="space-y-3 sm:space-y-4">
                  {giftItems.map((item) => (
                    <div
                      key={item.id}
                      className="bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl flex flex-col md:flex-row items-stretch border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] cursor-pointer hover:shadow-lg transition-shadow relative"
                    >
                      {item.mostWanted && (
                        <div className="absolute italic text-primary-750 top-2 right-2 bg-primary-150 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10">
                          📍MOST WANTED{" "}
                        </div>
                      )}
                      <div className="w-full md:w-[155px] h-[200px] sm:h-[180px] md:h-auto md:min-h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 text-center md:text-left p-4 sm:p-5">
                        <h3 className="text-lg sm:text-xl md:text-[22px] font-medium text-grey-750 mb-2">
                          {item.name}
                        </h3>
                        <p className="text-sm sm:text-base text-grey-100 mb-4">
                          {item.description}
                        </p>

                        <div className="flex flex-col sm:flex-row gap-2 items-center mb-4">
                          <div className="flex items-center gap-2 bg-light-blue-150 text-orange-700 px-3 py-2 sm:px-2.5 sm:py-1.5 rounded-full">
                            <Tag2 size={12} variant="Bulk" color="#5925DC" />
                            <span className="text-primary text-sm font-semibold">
                              ₦{item.price.toLocaleString()}
                            </span>
                          </div>

                          {item.reserved && (
                            <div className="flex items-center gap-1 font-bold italic bg-grin text-grin-100 text-sm px-3 py-2 sm:px-2.5 sm:py-1.5 rounded-full">
                              <svg
                                width="14"
                                height="14"
                                viewBox="0 0 14 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <rect
                                  x="1"
                                  y="1"
                                  width="12"
                                  height="12"
                                  rx="6"
                                  fill="#3CC35C"
                                />
                                <rect
                                  x="1"
                                  y="1"
                                  width="12"
                                  height="12"
                                  rx="6"
                                  stroke="#3CC35C"
                                  strokeWidth="2"
                                />
                                <path
                                  d="M9.18395 5.36426L6.18395 8.36426L4.82031 7.00062"
                                  stroke="white"
                                  strokeWidth="1.4"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                              <span>Reserved by you</span>
                            </div>
                          )}
                        </div>

                        {/* Crowd Gifting Section */}
                        {item.crowdGiftingEnabled && (
                          <div className="mb-4">
                            <div className="flex items-center gap-1 mb-2">
                              <TickCircle
                                size={16}
                                color="#3CC35C"
                                variant="Bold"
                                className="opacity-50"
                              />
                              <span className="text-sm font-medium text-grey-500">
                                Crowd gifting enabled
                              </span>
                            </div>

                            {/* Progress Bar */}
                            <div className="w-full max-w-[226px] mx-auto md:mx-0 bg-gray-200 rounded-full h-2 mb-2">
                              <div
                                className="bg-primary h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${
                                    (item.contributedAmount / item.price) * 100
                                  }%`,
                                }}
                              ></div>
                            </div>

                            <p className="text-sm text-grey-950 text-center md:text-left">
                              ₦{item.contributedAmount.toLocaleString()}{" "}
                              contributed by {item.contributorCount} people
                            </p>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex justify-center md:justify-end">
                          {item.purchased ? (
                            <button className="border border-primary-110 italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0">
                              Complete Purchase
                            </button>
                          ) : (
                            item.reserved &&
                            !item.purchased && (
                              <button
                                onClick={handleMakePayment}
                                className="border border-primary-110 text-white bg-primary italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0"
                              >
                                Make Payment
                              </button>
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                <div className="space-y-3 sm:space-y-4">
                  {cashItems.map((item) => (
                    <div
                      key={item.id}
                      className="bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl cursor-pointer hover:shadow-lg border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] transition-shadow"
                    >
                      <div className="flex flex-col-reverse md:flex-row items-stretch gap-4 min-h-[120px]">
                        <div className="w-full md:w-[155px] h-[200px] sm:h-[180px] md:h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                          <img
                            src={cash}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex flex-1 flex-col justify-between p-4 sm:p-5 md:py-4 md:pr-4">
                          <div className="text-center md:text-end mb-3 md:mb-0">
                            {item.status === "available" && (
                              <span className="text-xs font-bold italic text-primary-750 px-3 py-2 sm:px-2 sm:py-1 rounded-full bg-primary-150 uppercase tracking-wide">
                                Available
                              </span>
                            )}
                            {item.status === "reserved" && (
                              <span className="text-xs font-bold italic text-green-600 px-3 py-2 sm:px-2 sm:py-1 rounded-full bg-grin uppercase tracking-wide">
                                Reserved
                              </span>
                            )}
                            {item.status === "most_wanted" && (
                              <div className="flex justify-center md:justify-end items-center">
                                <div className="bg-primary-150 rounded-full px-3 py-2 sm:px-2 sm:py-1 italic font-bold gap-1">
                                  <span>📍</span>
                                  <span className="text-xs font-medium text-primary-750 uppercase tracking-wide">
                                    Most Wanted
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="flex-1 text-center md:text-start mb-4 md:mb-0">
                            <h3 className="text-xl sm:text-2xl md:text-[28px] font-extrabold text-grey-750 mb-2">
                              ₦{item.amount.toLocaleString()}
                            </h3>
                            <p className="text-sm sm:text-base text-grey-100 max-w-full md:max-w-[201px]">
                              {item.description}
                            </p>
                          </div>
                          <div className="flex w-full justify-center md:justify-end">
                            <button className="border border-primary-110 italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0">
                              Send Cashgift{" "}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};
